<!-- src/components/rank-time-selector.vue -->
<!-- 榜单时间选择器 - 基于 time-select 组件的业务封装 -->
<template>
    <div class="rank-time-selector">
        <!-- 标题区域 -->
        <div class="selector-header">
            <h3 class="title">选择查看时间</h3>
            <div class="current-info">
                <span class="current-time">{{ currentServerTime }}</span>
            </div>
        </div>
        
        <!-- 时间选择组件 -->
        <time-select 
            :auto-init="true"
            @date-change="handleDateChange"
            @time-change="handleTimeChange" />
        
        <!-- 选择结果展示 -->
        <div class="selection-result">
            <div class="result-item">
                <span class="label">查看日期：</span>
                <span class="value">{{ selectedDateText }}</span>
            </div>
            <div class="result-item">
                <span class="label">查看时段：</span>
                <span class="value">{{ selectedTimeText }}</span>
            </div>
            <div class="result-item" v-if="isHistoryData">
                <span class="label">数据状态：</span>
                <span class="value history">历史数据</span>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions">
            <button 
                class="action-btn primary"
                :disabled="!canConfirm"
                @click="handleConfirm">
                确认查看
            </button>
            <button 
                class="action-btn secondary"
                @click="handleReset">
                重置
            </button>
        </div>
    </div>
</template>

<script setup>
import TimeSelect from '@/components/time-select.vue';
import useTimeSelect from '@/stores/use-time-select-store';
import useActivityTime from '@/stores/modules/use-activity-time';
import dayjs from '@/utils/dayjs';

// Props
const props = defineProps({
    // 是否显示操作按钮
    showActions: {
        type: Boolean,
        default: true,
    },
    // 确认按钮文本
    confirmText: {
        type: String,
        default: '确认查看',
    },
});

// Emits
const emit = defineEmits(['confirm', 'reset', 'change']);

// Store
const timeStore = useTimeSelect();
const activityTimeStore = useActivityTime();

// 响应式数据
const selectedDate = ref('');
const selectedTime = ref('');

// 计算属性
const currentServerTime = computed(() => {
    if (!activityTimeStore.serverTime) return '获取中...';
    return dayjs.unix(activityTimeStore.serverTime).format('MM-DD HH:mm');
});

const selectedDateText = computed(() => {
    if (!selectedDate.value) return '未选择';
    return dayjs(selectedDate.value).format('MM月DD日');
});

const selectedTimeText = computed(() => {
    if (!selectedTime.value) return '未选择';
    const timeRange = timeStore.timeRanges.find(item => item.value === selectedTime.value);
    return timeRange?.rangeStr || '未选择';
});

const isHistoryData = computed(() => {
    if (!selectedDate.value || !selectedTime.value) return false;
    const selectedDateTime = dayjs(`${selectedDate.value} ${selectedTime.value}`);
    const currentTime = dayjs.unix(activityTimeStore.serverTime);
    return selectedDateTime.isBefore(currentTime);
});

const canConfirm = computed(() => {
    return selectedDate.value && selectedTime.value;
});

// 方法
function handleDateChange(date) {
    selectedDate.value = date;
    emitChange();
}

function handleTimeChange(time) {
    selectedTime.value = time;
    emitChange();
}

function emitChange() {
    emit('change', {
        date: selectedDate.value,
        time: selectedTime.value,
        dateText: selectedDateText.value,
        timeText: selectedTimeText.value,
        isHistory: isHistoryData.value,
    });
}

function handleConfirm() {
    if (!canConfirm.value) {
        showToast('请先选择日期和时间段');
        return;
    }
    
    emit('confirm', {
        date: selectedDate.value,
        time: selectedTime.value,
        dateText: selectedDateText.value,
        timeText: selectedTimeText.value,
        isHistory: isHistoryData.value,
    });
}

function handleReset() {
    timeStore.resetNull();
    selectedDate.value = '';
    selectedTime.value = '';
    emit('reset');
    showToast('已重置选择');
}

// 监听 store 中的选择变化
watch(() => timeStore.selectedDate, (newDate) => {
    selectedDate.value = newDate;
});

watch(() => timeStore.selectedTimeRange, (newTime) => {
    selectedTime.value = newTime;
});

// 暴露方法给父组件
defineExpose({
    reset: handleReset,
    getSelection: () => ({
        date: selectedDate.value,
        time: selectedTime.value,
        dateText: selectedDateText.value,
        timeText: selectedTimeText.value,
        isHistory: isHistoryData.value,
    }),
});
</script>

<style lang="less" scoped>
.rank-time-selector {
    width: 100%;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.selector-header {
    padding: 16px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    
    .title {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 8px 0;
    }
    
    .current-info {
        .current-time {
            font-size: 12px;
            opacity: 0.9;
        }
    }
}

.selection-result {
    padding: 16px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    
    .result-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        &:last-child {
            margin-bottom: 0;
        }
        
        .label {
            font-size: 14px;
            color: #666;
            min-width: 80px;
        }
        
        .value {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            
            &.history {
                color: #ff6b6b;
            }
        }
    }
}

.actions {
    padding: 16px 20px;
    display: flex;
    gap: 12px;
    
    .action-btn {
        flex: 1;
        height: 44px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        
        &.primary {
            background-color: #007aff;
            color: #fff;
            
            &:hover:not(:disabled) {
                background-color: #0056cc;
            }
            
            &:disabled {
                background-color: #ccc;
                cursor: not-allowed;
            }
        }
        
        &.secondary {
            background-color: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
            
            &:hover {
                background-color: #e9ecef;
                color: #333;
            }
        }
    }
}

// 响应式设计
@media (max-width: 375px) {
    .selector-header {
        padding: 12px 16px;
        
        .title {
            font-size: 15px;
        }
    }
    
    .selection-result {
        padding: 12px 16px;
    }
    
    .actions {
        padding: 12px 16px;
        
        .action-btn {
            height: 40px;
            font-size: 13px;
        }
    }
}
</style>
