<!-- src/components/rank-time-selector.vue -->
<!-- 榜单时间选择器 - 基于 time-select 组件的业务封装 -->
<template>
    <!-- 时间选择组件 -->
    <time-select
        :auto-init="true"
        @date-change="handleDateChange"
        @time-change="handleTimeChange" />
</template>

<script setup>
import TimeSelect from '@/components/tab1/time-select.vue';
import useTimeSelect from '@/stores/use-time-select-store';
import useActivityTime from '@/stores/modules/use-activity-time';
import dayjs from '@/utils/dayjs';

// Props
const props = defineProps({
    // 是否显示操作按钮
    showActions: {
        type: Boolean,
        default: true,
    },
    // 确认按钮文本
    confirmText: {
        type: String,
        default: '确认查看',
    },
});

// Emits
const emit = defineEmits(['confirm', 'reset', 'change']);

// Store
const timeStore = useTimeSelect();
const activityTimeStore = useActivityTime();

// 响应式数据
const selectedDate = ref('');
const selectedTime = ref('');

// 计算属性
const currentServerTime = computed(() => {
    if (!activityTimeStore.serverTime)
        return '获取中...';
    return dayjs.unix(activityTimeStore.serverTime).format('MM-DD HH:mm');
});

const selectedDateText = computed(() => {
    if (!selectedDate.value)
        return '未选择';
    return dayjs(selectedDate.value).format('MM月DD日');
});

const selectedTimeText = computed(() => {
    if (!selectedTime.value)
        return '未选择';
    const timeRange = timeStore.timeRanges.find(item => item.value === selectedTime.value);
    return timeRange?.rangeStr || '未选择';
});

const isHistoryData = computed(() => {
    if (!selectedDate.value || !selectedTime.value)
        return false;
    const selectedDateTime = dayjs(`${selectedDate.value} ${selectedTime.value}`);
    const currentTime = dayjs.unix(activityTimeStore.serverTime);
    return selectedDateTime.isBefore(currentTime);
});

const canConfirm = computed(() => {
    return selectedDate.value && selectedTime.value;
});

// 方法
function handleDateChange(date) {
    selectedDate.value = date;
    emitChange();
}

function handleTimeChange(time) {
    selectedTime.value = time;
    emitChange();
}

function emitChange() {
    emit('change', {
        date: selectedDate.value,
        time: selectedTime.value,
        dateText: selectedDateText.value,
        timeText: selectedTimeText.value,
        isHistory: isHistoryData.value,
    });
}

function handleConfirm() {
    if (!canConfirm.value) {
        showToast('请先选择日期和时间段');
        return;
    }

    emit('confirm', {
        date: selectedDate.value,
        time: selectedTime.value,
        dateText: selectedDateText.value,
        timeText: selectedTimeText.value,
        isHistory: isHistoryData.value,
    });
}

function handleReset() {
    timeStore.resetNull();
    selectedDate.value = '';
    selectedTime.value = '';
    emit('reset');
    showToast('已重置选择');
}

// 监听 store 中的选择变化
watch(() => timeStore.selectedDate, (newDate) => {
    selectedDate.value = newDate;
});

watch(() => timeStore.selectedTimeRange, (newTime) => {
    selectedTime.value = newTime;
});

// 暴露方法给父组件
defineExpose({
    reset: handleReset,
    getSelection: () => ({
        date: selectedDate.value,
        time: selectedTime.value,
        dateText: selectedDateText.value,
        timeText: selectedTimeText.value,
        isHistory: isHistoryData.value,
    }),
});
</script>

<style lang="less" scoped>

</style>
