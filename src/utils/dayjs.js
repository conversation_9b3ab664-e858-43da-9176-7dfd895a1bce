import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import duration from 'dayjs/plugin/duration';
import { extend, locale, unix, isDayjs, Dayjs } from "dayjs";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(duration);

dayjs.tz.setDefault("Asia/Shanghai");
const newDayjs = (...args) => dayjs.tz(...args);

newDayjs.extend = extend;
newDayjs.locale = locale;
newDayjs.unix = unix;
newDayjs.isDayjs = isDayjs;
newDayjs.Dayjs = Dayjs;
export { PluginFunc } from "dayjs";

export default newDayjs;