<!-- eslint-disable vue/no-parsing-error -->
<!-- src/components/time-select.vue -->
<template>
    <div class="time-select">
        <!-- 时间段选择区域 -->
        <div class="time-section">
            <!-- 箭头切换 -->
            <div class="time-controls">
                <div
                    class="time-btn prev-btn"
                    @click="handlePrevTime">
                    <<上一时段
                </div>

                <!-- 当前时间段显示和下拉选择 -->
                <div class="time-display-wrapper">
                    <div
                        class="time-display"
                        @click="toggleTimeDropdown">
                        {{ currentTimeRangeText }}
                        <img
                            class="dropdown-arrow h-[8px] w-[8px]"
                            :class="{ open: showTimeDropdown }"
                            :src="requireImg('<EMAIL>')"
                        ></img>
                    </div>

                    <!-- 时间段下拉框 -->
                    <div
                        v-show="showTimeDropdown"
                        class="time-dropdown">
                        <div
                            v-for="timeRange in timeStore.timeRanges"
                            :key="timeRange.value"
                            class="time-option"
                            :class="{ active: timeRange.value === timeStore.selectedTimeRange }"
                            @click="handleTimeRangeSelect(timeRange.value)">
                            {{ timeRange.rangeStr }}
                        </div>
                    </div>
                </div>

                <div
                    class="time-btn next-btn"
                    @click="handleNextTime">
                    下一时段>>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import useTimeSelect from '@/stores/use-time-select-store';

// Props
const props = defineProps({
    // 是否自动初始化
    autoInit: {
        type: Boolean,
        default: true,
    },
});

// Emits
const emit = defineEmits(['dateChange', 'timeChange']);

// Store
const timeStore = useTimeSelect();

// 响应式数据
const showTimeDropdown = ref(false);

// 计算属性
const currentTimeRangeText = computed(() => {
    if (!timeStore.selectedTimeRange) {
        return '请选择时段';
    }
    const currentRange = timeStore.timeRanges.find(
        item => item.value === timeStore.selectedTimeRange,
    );
    return currentRange?.rangeStr || '请选择时段';
});

// 方法
function handleDateChange() {
    emit('dateChange', timeStore.selectedDate);
}

function handlePrevTime() {
    timeStore.prevTimeRange();
    emit('timeChange', timeStore.selectedTimeRange);
}

function handleNextTime() {
    timeStore.nextTimeRange();
    emit('timeChange', timeStore.selectedTimeRange);
}

function toggleTimeDropdown() {
    showTimeDropdown.value = !showTimeDropdown.value;
}

function handleTimeRangeSelect(value) {
    timeStore.changeTimeRange(value);
    showTimeDropdown.value = false;
    emit('timeChange', timeStore.selectedTimeRange);
}

// 点击外部关闭下拉框
function handleClickOutside(event) {
    const dropdown = event.target.closest('.time-display-wrapper');
    if (!dropdown) {
        showTimeDropdown.value = false;
    }
}

// 手动关闭下拉框
function closeDropdown() {
    showTimeDropdown.value = false;
}

// 生命周期
onMounted(() => {
    if (props.autoInit) {
        timeStore.init();
    }
    // 添加全局点击事件监听
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    // 移除全局点击事件监听
    document.removeEventListener('click', handleClickOutside);
});

// 监听选中值变化
watch(() => timeStore.selectedDate, (newDate) => {
    emit('dateChange', newDate);
});

watch(() => timeStore.selectedTimeRange, (newTime) => {
    emit('timeChange', newTime);
});
</script>

<style lang="less" scoped>
.time-select {
    width: 100%;
}

.time-section {
    .time-controls {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;

        .time-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 12px;
            color: #db6cff;
            .arrow-left,
            .arrow-right {
            }
        }

        .time-display-wrapper {
            position: relative;

            .time-display {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 115px;
                height: 29px;
                background-image: url('@/assets/img/<EMAIL>');
                background-size: 100% 100%;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s;
                color: #ffffff;

                .dropdown-arrow {
                    transition: transform 0.2s;

                    &.open {
                        transform: rotate(180deg);
                    }
                }
            }

            .time-dropdown {
                position: absolute;
                top: 110%;
                left: 50%;
                transform: translateX(-50%);
                z-index: 1000;
                background-image: url('@/assets/img/<EMAIL>');
                width: 105.5px;
                height: 142px;
                background: #6443cf;
                border-radius: 3px;
                overflow: hidden;

                .time-option {
                    margin: 0 auto;
                    color: #ffffff;
                    font-size: 11px;
                    cursor: pointer;
                    transition: background-color 0.2s;
                    width: 98.5px;
                    height: 23px;
                    display: flex;
                    .flex-center();

                    &.active {
                        background-image: url('@/assets/img/<EMAIL>');
                        background-size: 100% 100%;
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 375px) {
    .time-select {
        padding: 12px;
    }

    .time-controls {
        .time-btn {
            padding: 6px 8px;
            font-size: 11px;
        }

        .time-display {
            font-size: 13px;
        }
    }
}
</style>
