import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/esm/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/esm/plugin/isSameOrBefore';
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

/**
 * 检查目标时间是否在某个时间范围内
 * （包含起始和结束时间）
 * @param {*} target 目标时间
 * @param {*} start 开始时间
 * @param {*} end 结束时间
 * @returns
 */
export function checkInTimeRange(target, start, end) {
    if (!target || !start || !end) return false;
    try {
        const t = dayjs(target);
        return t.isSameOrAfter(dayjs(start)) && t.isSameOrBefore(dayjs(end));
    } catch (error) {
        console.log(error);
    }
    return false;
}

/**
 * 检查是否是目标星期
 * @param {*} date
 * @param {*} day 0-6，0表示周日
 * @returns
 */
export function checkIsTargetDay(date, day) {
    if (typeof day !== 'number' || !date) return false;
    try {
        return dayjs(date).day() === day;
    } catch (error) {
        console.log(error);
    }
    return false;
}

/**
 * 检查某个时间是否在当天的某个时间段内
 */
export function checkInTargetDateTimeRange(target, startTimeStr, endTimeStr) {
    if (!target || !startTimeStr || !endTimeStr) return false;
    try {
        const t = dayjs(target);
        const startDateStr = t.format('YYYY-MM-DD'); // 某个时间的当天日期
        let endDateStr = t.format('YYYY-MM-DD'); // 某个时间的当天日期
        if (endTimeStr == '00') {
            endDateStr = t.add(1, 'day').format('YYYY-MM-DD');
        }
        const startT = dayjs(`${startDateStr} ${startTimeStr}`).clone().millisecond(0);
        const endT = dayjs(`${endDateStr} ${endTimeStr}`).clone().millisecond(999);
        return t.isSameOrAfter(startT) && t.isSameOrBefore(endT);
    } catch (error) {
        console.log(error);
    }
    return false;
}
