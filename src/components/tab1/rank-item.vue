<template>
    <div
        class="rank-item"
        :class="[isSelf && 'is-self', `top${rank}`]">
        <div class="rank-number">{{ rank }}</div>

        <div class="user-info">
            <div class="avatar">
                <img
                    :src="getAvatar(item.username)"
                    class="border-avatar h-full w-full rounded-full"
                    @click="toPerson(item.username)" />
                <room-status-anchor
                    v-if="item.channelInfo?.channelId && !isSelf"
                    :cid="item.channelInfo?.channelId"
                    :status="item.channelInfo?.status"
                    :is-anchor="isAnchor"
                    class="room-status-anchor" />
            </div>

            <div class="nickname">
                {{ safeOmitTxt(item.nickname, 6) }}
                <div class="mt-4 text-10">{{ isAnchor ? `ID:${item.alias}` : '荣耀骑士' }}</div>
            </div>
        </div>

        <div class="rank-value">
            {{ omitValue(item.value) }}
            <slot></slot>
        </div>
        <template v-if="isSelf">
            <div
                v-if="item.ltPrevValueHuman"
                class="absolute left-11 top-22 text-10 text-[#FFFFFF]">
                {{ item.ltPrevValueHuman }}
            </div>
            <div
                v-if="item.gtNextValueHuman"
                class="absolute right-11 top-22 text-10 text-[#FFFFFF]">
                {{ item.gtNextValueHuman }}
            </div>
        </template>
    </div>
</template>

<script setup>
import { formatNumber } from '@/utils';

const props = defineProps({
    item: {
        type: Object,
        required: true,
    },
    rank: {
        type: Number,
        required: true,
    },
    type: {
        type: Number,
        required: true,
    },
    isSelf: {
        type: Boolean,
        default: false,
    },
    isAnchor: {
        type: Boolean,
        default: false,
    },
});

function omitValue(value) {
    return formatNumber(value || 0);
}
</script>

<style lang="less" scoped>
.rank-item {
    display: flex;
    align-items: center;
    padding: 8px 14px;
    width: 342.5px;
    height: 51px;
    background-image: url('@/assets/img/<EMAIL>');
    background-size: 100%;
    margin-bottom: 4px;
    margin-right: auto;
    margin-left: auto;

    &.is-self {
        background-image: url('@/assets/img/<EMAIL>');
        background-size: 100%;
        width: 375px;
        height: 60.5px;
        position: fixed;
        bottom: 50px;
        right: 0;
        z-index: 100;
        margin-bottom: 0;
        padding-top: 22px;
    }

    &.top1 {
        background-image: url('@/assets/img/<EMAIL>');
        background-size: 100%;
    }
    &.top2 {
        background-image: url('@/assets/img/<EMAIL>');
        background-size: 100%;
    }
    &.top3 {
        background-image: url('@/assets/img/<EMAIL>');
        background-size: 100%;
    }

    .rank-number {
        width: 30px;
        font-size: 12px;
        color: #ffffff;
        text-align: center;
        flex-shrink: 0;
    }

    .user-info {
        display: flex;
        align-items: center;
        flex: 1;
        margin-left: 10px;
        min-width: 0;

        .avatar {
            position: relative;
            width: 34.5px;
            height: 34.5px;
            background: #271a45;
            border-radius: 50%;

            flex-shrink: 0;
        }
        .border-avatar {
            border: 1px solid #d8a74d;
        }

        .nickname {
            margin-left: 6px;
            font-size: 12px;
            color: #ffffff;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .rank-value {
        font-size: 13px;
        color: #ffe169;
        flex-shrink: 0;
        margin-left: 15px;
        text-align: right;
    }
}
.room-status-anchor {
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}
</style>
