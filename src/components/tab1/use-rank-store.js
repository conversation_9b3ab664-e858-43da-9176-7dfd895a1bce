// src/components/tab4/use-rank-store.js
import { defineStore } from 'pinia';
import { getKnightRank as getRank } from '@/api';
import { getAnchorUid } from '@/utils/jsbridge.js';

const useRankStore = defineStore('rank', () => {
    const list = ref([]);
    const selfRank = ref(null);
    const loading = ref(false);
    const error = ref({ hasError: false, message: '', canRetry: false });

    let page = 1;
    const SIZE = 20;
    let hasMore = true;
    let total = 0;

    // 标准化用户数据格式
    function normalizeUserData(user) {
        return {
            uid: user.uid || user.userInfo?.uid,
            username: user.username || user.userInfo?.username || '虚位以待',
            nickname: user.nickname || user.userInfo?.nickname,
            alias: user.alias || user.userInfo?.alias,
            value: user.value || user.amount || user.score || 0,
            rank: user.rankHuman || user.rank || user.ranking || 0,
            channelInfo: user.channelInfo || user.userInfo?.channelInfo,
            gtNextValueHuman: user.gtNextValueHuman,
            ltPrevValueHuman: user.ltPrevValueHuman,
            mvpInfoList: user.mvpInfoList,
        };
    }
    // 重置分页状态
    function resetPagination() {
        page = 1;
        hasMore = true;
        total = 0;
        list.value = [];
    }

    // 重置错误状态
    function resetError() {
        error.value = { hasError: false, message: '', canRetry: false };
    }

    // 设置错误状态
    function setError(message, canRetry = true) {
        error.value = { hasError: true, message, canRetry };
    }

    // 获取榜单数据
    async function queryRankList(isLoadMore = false) {
        if (!hasMore && isLoadMore)
            return;

        if (loading.value)
            return;

        // 重置错误状态
        if (!isLoadMore) {
            resetError();
        }

        loading.value = true;

        try {
            const [res] = await getRank({
                page,
                size: SIZE,
                anchorUid: getAnchorUid(),
            });

            if (res?.code === 0) {
                const { data } = res;
                const rawList = data.list || data.ranks || [];
                const newList = rawList.map(normalizeUserData);
                list.value = isLoadMore ? [...list.value, ...newList] : newList;
                // 处理自己的排名数据
                if (data.self || data.mine || data.my) {
                    const selfData = data.self || data.mine || data.my;
                    selfRank.value = normalizeUserData(selfData);
                }
                else {
                    selfRank.value = null;
                }

                total = data.total || data.totalCount || 0;
                hasMore = list.value.length < total && newList.length === SIZE;

                if (newList.length > 0) {
                    page += 1;
                }

                // 成功后重置错误状态
                resetError();
            }
        }
        catch (error) {
            console.error('获取榜单数据异常：', error);
            const errorMsg = '网络错误，请检查网络连接';

            if (!isLoadMore) {
                setError(errorMsg, true);
                showToast(errorMsg);
            }
        }
        finally {
            loading.value = false;
        }
    }

    // 重试获取数据
    async function retryQuery() {
        resetPagination();
        await queryRankList();
    }

    // 加载更多
    async function loadMore() {
        await queryRankList(true);
    }

    // 获取前三名数据
    const topThreeList = computed(() => {
        const top3 = list.value.slice(0, 3);

        // 补齐3个位置，空位用占位数据
        const result = Array.from({ length: 3 }, (_, index) => {
            return top3[index] || normalizeUserData({
                rank: index + 1,
                isEmpty: true,
                userInfo: {
                    nickname: '虚位以待',
                    avatar: '',
                },
                value: 0,
            });
        });

        return result;
    });

    // 获取第4名及以后的数据
    const normalList = computed(() => list.value.slice(3));

    // 是否有自己的排名
    const hasSelfRank = computed(() => !!selfRank.value);

    // 是否显示空状态
    const showEmptyState = computed(() =>
        !loading.value.list && !error.value.hasError && list.value.length === 0,
    );

    return {
        list,
        selfRank,
        loading,
        error,
        hasMore,
        topThreeList,
        normalList,
        hasSelfRank,
        showEmptyState,
        queryRankList,
        retryQuery,
        loadMore,
    };
});

export default useRankStore;
