# Time Select 时间段选择组件

## 概述

Time Select 是一个用于时间段选择的 Vue 3 组件，支持日期选择和6个时间段的选择功能。组件基于项目的活动时间管理需求设计，提供了完整的时间段选择交互体验。

## 功能特性

### 日期选择功能
- **默认定位规则**：
  - 活动进行中：默认定位至当天日期
  - 活动结束后：默认定位至活动最后一天（9月21日）
- 支持用户手动选择其他日期
- 支持查看已结束日期的榜单数据

### 时段选择功能
- **6个时间段**：18-19点、19-20点、20-21点、21-22点、22-23点、23-24点
- **默认定位**：默认定位至当前时段
- **时段切换方式**：
  1. **箭头按钮切换**：
     - 点击【上一时段】/【下一时段】按钮可在当日不同时段间切换
     - 边界处理：到达第一时段/最后时段时，再次点击显示 toast 提示"没有更多了~"

  2. **下拉框选择**：
     - 支持点击时段下拉框进行选择
     - 下拉框固定展示每天的6个时段选项
     - 用户可自由选择任意时段
     - 下拉框需要手动点击才能收起（不自动收起）

### 状态处理
- **未开启时段**：点击未开启的时段时，显示 toast 提示"该时段暂未开启~"
- **历史数据**：支持回切查看已结束的榜单数据

## 组件结构

### 核心组件

1. **useTimeSelect Store** (`src/stores/use-time-select-store.js`)
   - 整合了日期和时间段选择的状态管理
   - 提供完整的选择逻辑和验证功能

2. **TimeSelect 组件** (`src/components/time-select.vue`)
   - 基础的时间选择UI组件
   - 提供日期选择、时段切换、下拉选择功能

3. **RankTimeSelector 组件** (`src/components/rank-time-selector.vue`)
   - 基于 TimeSelect 的业务封装
   - 适用于榜单页面的时间选择场景

## 使用方法

### 1. 基础使用

```vue
<template>
    <time-select
        :auto-init="true"
        @date-change="handleDateChange"
        @time-change="handleTimeChange" />
</template>

<script setup>
import TimeSelect from '@/components/time-select.vue';

function handleDateChange(date) {
    console.log('日期变更:', date);
}

function handleTimeChange(time) {
    console.log('时间段变更:', time);
}
</script>
```

### 2. 业务组件使用

```vue
<template>
    <rank-time-selector
        :show-actions="true"
        confirm-text="查看榜单"
        @confirm="handleTimeConfirm"
        @change="handleTimeChange"
        @reset="handleTimeReset" />
</template>

<script setup>
import RankTimeSelector from '@/components/rank-time-selector.vue';

function handleTimeConfirm(selection) {
    console.log('确认查看时间:', selection);
    // selection 包含: { date, time, dateText, timeText, isHistory }
}
</script>
```

### 3. Store 直接使用

```javascript
import useTimeSelect from '@/stores/use-time-select-store';

const timeStore = useTimeSelect();

// 初始化
timeStore.init();

// 选择日期
timeStore.selectDate('2025-09-15');

// 选择时间段
timeStore.changeTimeRange('19:00:00');

// 切换时间段
timeStore.nextTimeRange();
timeStore.prevTimeRange();

// 获取当前选择
console.log(timeStore.selectedDate);
console.log(timeStore.selectedTimeRange);
```

## API 文档

### TimeSelect Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| autoInit | Boolean | true | 是否自动初始化 |

### TimeSelect Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| dateChange | date: string | 日期变更时触发 |
| timeChange | time: string | 时间段变更时触发 |

### RankTimeSelector Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| showActions | Boolean | true | 是否显示操作按钮 |
| confirmText | String | '确认查看' | 确认按钮文本 |

### RankTimeSelector Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| confirm | selection: object | 确认选择时触发 |
| change | selection: object | 选择变更时触发 |
| reset | - | 重置选择时触发 |

### Store Methods

| 方法名 | 参数 | 说明 |
|--------|------|------|
| init() | - | 初始化选择 |
| selectDate(date) | date: string | 选择日期 |
| changeTimeRange(time) | time: string | 选择时间段 |
| nextTimeRange() | - | 下一时间段 |
| prevTimeRange() | - | 上一时间段 |
| reset() | - | 重置为初始状态 |
| resetNull() | - | 清空所有选择 |

## 样式定制

组件使用 Less 编写样式，支持响应式设计。主要的样式变量：

```less
// 主色调
@primary-color: #007aff;
@border-color: #ddd;
@background-color: #fff;

// 响应式断点
@mobile-breakpoint: 375px;
```

## 注意事项

1. **时间段范围**：组件支持18-24点共6个时间段，与原有的19-20、22-23两个时段不同
2. **下拉框行为**：时间段下拉框不会自动收起，需要用户手动点击关闭按钮或点击外部区域
3. **状态验证**：组件会自动验证选择的时间段是否已开启，未开启时会显示提示
4. **历史数据**：支持查看历史时间段的数据，会自动标识为历史数据

## 示例页面

- `src/pages/time-select-demo.vue` - 基础组件演示
- `src/pages/rank-page-example.vue` - 完整的榜单页面示例

## 依赖

- Vue 3
- Pinia
- dayjs
- 项目内的 useActivityTime store
- 项目内的工具函数 (showToast, checkInTargetDateTimeRange 等)
