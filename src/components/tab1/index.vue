<template>
    <div class="flex flex-col items-center">
        <van-list
            :finished="!rankStore.hasMore"
            offset="30"
            class="w-full flex flex-col items-center"
            @load="handleLoadMore"
        >
            <div
                class="bg-default h-[41px] w-[240.5px] flex items-center justify-center"
                :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
            >
                <span
                    v-if="initStore.areaName"
                    class="text-[#F9E174]">
                    本活动中你的所属赛区: {{ initStore.areaName || '' }}
                </span>
                <span
                    v-else
                    class="text-[#C6A6FF]">您暂不符合参赛条件</span>
            </div>
            <div class="mt-7 flex flex-col items-center text-center text-11 text-[#C6A6FF] leading-[14px]">
                活动期间每日18:00-24:00限时开启小时榜<br />全站达人由系统分配至3个赛区比赛<br />根据各赛道达人【每小时开启听听期间收礼值】排行 (1豆=1争霸值）
            </div>

            <div
                class="bg-default mt-10 flex items-center justify-center"
            >
                <div
                    v-for="item in rewardList"
                    class="bg-default mx-[10px] flex flex-shrink-0 flex-col items-center"
                >
                    <div
                        class="bg-default flex-center flex-center h-[58.5px] w-[60px] flex"
                        :style="`background-image: url(${requireImg('<EMAIL>')})`">
                        <img
                            :src="item.imageUrl"
                            class="h-80% w-80% object-contain"
                            alt="">
                    </div>
                    <div class="mt-[5px] text-[11px] text-[#C6A6FF] leading-[12px]">{{ item.name }}</div>
                </div>
            </div>
            <div class="mt-10 flex justify-between">
                <div
                    v-for="item in navStore.SUB_NAV"
                    class="bg-default flex-center mx-3 h-[34px] w-[118px] flex text-18"
                    :style="{ backgroundImage: `url(${item.value === navStore.subNav ? requireImg('<EMAIL>') : requireImg('<EMAIL>')})` }"
                    @click="navStore.changeSubNav(item.value)"
                >
                    <div
                        :class="[item.value === navStore.subNav ? 'active-text' : 'text']"
                    >
                        {{ item.name }}
                    </div>
                </div>
            </div>
            <div class="mt-16 flex justify-between">
                <div
                    v-for="item in timeSelectStore.dates"
                    class="bg-default flex-center mx-4 h-[25.5px] w-[75px] flex text-13"
                    :style="{ backgroundImage: `url(${item.value === timeSelectStore.selectedDate ? requireImg('<EMAIL>') : requireImg('<EMAIL>')})` }"
                    @click="timeSelectStore.selectDate(item.value)"
                >
                    <div
                        :class="[item.value === timeSelectStore.selectedDate ? 'active-text' : 'text']"
                    >
                        {{ item.dateStr }}
                    </div>
                </div>
            </div>
            <div
                class="bg-default rank-list relative mt-9 flex flex-col items-center"
                :class="[rankStore.hasSelfRank ? 'pb-130' : '']"
            >
                <div class="mt-22 text-[11px] text-[#C6A6FF]">
                    <span class="text-[#F9E174]">本时段31:06后结榜，</span>赛区TOP10且达100w值可得公会积分
                </div>
                <rank-time-selector class="mt-10"></rank-time-selector>
                <div
                    class="bg-default mt-10 h-[18.5px] w-[344.5px] flex items-center text-10 text-[#A08CCA]"
                    :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                >
                    <span class="ml-20">排名</span>
                    <span class="ml-68">昵称</span>
                    <span class="ml-175">争霸值</span>
                </div>
                <!-- 错误状态 -->
                <div
                    v-if="rankStore.error.hasError"
                    class="error-state">
                    <div class="error-icon">⚠️</div>
                    <div class="error-message">{{ rankStore.error.message }}</div>
                    <div
                        v-if="rankStore.error.canRetry"
                        class="retry-btn"
                        @click="handleRetry"
                    >
                        重新加载
                    </div>
                </div>

                <!-- 正常内容 -->
                <template v-else>
                    <!-- 普通排名列表 -->
                    <div
                        v-if="rankStore.list.length > 0"
                        class="normal-list mt-7">
                        <rank-item
                            v-for="(item, index) in rankStore.list"
                            :key="`${rankStore.currentType}-${item.uid || item.id}-${index}`"
                            :item="item"
                            :rank="index + 1"
                            :type="rankStore.currentType"
                            :is-anchor="false"
                        >
                            <div
                                class="flex-center bg-default h-[14px] w-[116.5px] flex text-10 text-[#FFFFFF]"
                                :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }">
                                <span v-if="item.points">预计所属公会可得{{ item.points }}积分</span>
                                <span v-else>暂未达到门槛值</span>
                            </div>
                        </rank-item>
                    </div>

                    <!-- 自己的排名 -->
                    <div
                        v-if="rankStore.hasSelfRank"
                        class="self-rank">
                        <rank-item
                            :is-anchor="false"
                            :item="rankStore.selfRank"
                            :rank="rankStore.selfRank.rank || rankStore.selfRank.ranking"
                            :is-self="true"
                        />
                    </div>

                    <!-- 空状态 -->
                    <div
                        v-if="rankStore.showEmptyState"
                        class="mt-30 text-[#C6A6FF] text-[13]">
                        <img
                            class="mx-auto mb-7 h-[49.5px] w-[76px]"
                            :src="requireImg('<EMAIL>')"
                            alt="">
                        <div class="empty-text">暂无数据</div>
                    </div>

                    <!-- 加载更多提示 -->
                    <div
                        v-if="rankStore.loading.more"
                        class="loading-more">
                        <div class="loading-spinner"></div>
                        <span>加载中...</span>
                    </div>

                    <!-- 没有更多数据 -->
                    <div
                        v-if="!rankStore.hasMore && rankStore.list.length > 0"
                        class="no-more">
                        <div class="no-more-line"></div>
                        <span>没有更多数据了</span>
                        <div class="no-more-line"></div>
                    </div>
                </template>
            </div>
        </van-list>
    </div>
</template>

<script setup>
import useNav from '../tab/use-nav';
import useRankStore from './use-rank-store';
import dayjs from '@/utils/dayjs';
import { getRewardInfo, normalizeUserData } from '@/auto-imports/my-utils';
import useLoading from '@/hooks/use-loading';
import useInitStore from '@/stores/modules/use-init-store';
import useTimeSelect from '@/stores/use-time-select-store';

// 如果当前时间在  6个时间段：18-19、19-20、20-21、21-22、22-23、23-24 中，且selectedTimeRange选中l 显示当前的倒计时
const remainTimeStr
 = computed(() => {
     if (isEnd.value) {
         return '';
     }
     const curTime = dayjs.unix(serverTime.value).tz('Asia/Shanghai');
     const endTime = dayjs.unix('当前小时结束时间').tz('Asia/Shanghai');

     let remainStr = '';
     // 判断活动状态
     if (curTime.isBefore(endTime)) {
         const diff = endTime.diff(curTime, 'second');
         remainStr = dayjs.duration(Math.max(0, diff), 'second').format('D天HH时mm分ss秒');
     }
     return remainStr;
 });

const navStore = useNav();

const initStore = useInitStore();

const timeSelectStore = useTimeSelect();

const rankStore = useRankStore();
useLoading(computed(() => rankStore.loading));

const rewardList = [
    { ...getRewardInfo('A1'), name: '粉V认证' },
    { ...getRewardInfo('A13'), name: '粉团铭牌' },
    { ...getRewardInfo('A14'), name: '亲密值加倍' },
    { ...getRewardInfo('B1'), name: '公会积分' },
];

// 加载更多
const handleLoadMore = async () => {
    if (rankStore.loading || !rankStore.hasMore) {
        return;
    }
    try {
        await rankStore.loadMore();
    }
    catch {

    }
};
// 日期切换
watch(
    () => timeSelectStore.selectedDate,
    (val) => {
        if (!val)
            return;
        rankStore.retryQuery();
    },
);

watch(() => timeSelectStore.selectedTimeRange, (val) => {
    if (!val)
        return;
    rankStore.retryQuery();
});

watch(() => navStore.subNav, (val) => {
    if (!val)
        return;
    rankStore.retryQuery();
});

onMounted(async () => {
    if (initStore.areaName) {
        navStore.changeSubNav(initData.value.hourRankRaceId);
    }
    setTimeout(() => {
        rankStore.retryQuery();
    }, 500);
});
</script>

<style lang="less" scoped>
.rank-list {
    width: 100%;
    height: 100%;
    .point-9-px(url('@/assets/img/<EMAIL>'),352, 610, 750, 0, 1, 0,2);
    border-top: 0;
    border-bottom: 0;
    border-left: 0;
    border-right: 0;
    height: auto;
    min-height: 610px;
    width: 352px;
}

.active-text {
    background-image: linear-gradient(to bottom, #ffffff, #fae37c, #fd9c60); // 背景线性渐变
    color: #ffffff; // 兜底颜色，防止文字裁剪不生效
    background-clip: text;
    -webkit-background-clip: text; // 背景被裁减为文字的前景色
    -webkit-text-fill-color: transparent; // 文字填充为透明，优先级比color高。
    font-family: 'HYLINGXINTIJ';
    line-height: initial;
}
.text {
    background-image: linear-gradient(to bottom, #ffffff, #e398f3, #c6a6ff); // 背景线性渐变
    color: #ffffff; // 兜底颜色，防止文字裁剪不生效
    background-clip: text;
    -webkit-background-clip: text; // 背景被裁减为文字的前景色
    -webkit-text-fill-color: transparent; // 文字填充为透明，优先级比color高。
    font-family: 'HYLINGXINTIJ';
    line-height: initial;
}
</style>
