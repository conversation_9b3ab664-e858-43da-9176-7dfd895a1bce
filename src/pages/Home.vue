<route lang="json">
{
    "name": "Home",
    "path": "/:pathMatch(.*)*",
    "meta": {
        "title": "积分争霸赛"
    }
}
</route>

<template>
    <page-container>
        <div class="home pt-191">
            <img
                class="absolute right-9 top-45 z-100 h-[20px] w-[45px]"
                :src="requireImg('<EMAIL>')"
                @click="toRule"
            >
            <img
                v-if="initStore.userInitData?.hasPermission"
                :src="requireImg('<EMAIL>')"
                class="absolute right-8 top-72 z-100 h-[43.5px] w-[48px]">
            <tab class="fixed bottom-0 right-0 z-100"></tab>
            <template v-if="initStore.initData.serverTime">
                <tab1 v-if="navStore.current === 1"></tab1>
                <tab2 v-if="navStore.current === 2"></tab2>
            </template>
        </div>
    </page-container>
</template>

<script setup name="Home">
import useInitStore from '@/stores/modules/use-init-store';
import useNavStore from '@/components/tab/use-nav';
import { pageView, track } from '@/utils/jsbridge';

const navStore = useNavStore();

const initStore = useInitStore();

const toRule = () => {
    track({ event_id: 'rule_click' });
    jumpToLink('/web/beluga-activity-68ad7d78fdfc504017c4b499/index.html');
};

onMounted(async () => {
    pageView('activity_page');
    const toast = showLoading();
    await initStore.init();
    await initStore.userInit();
    toast.close();
});
</script>

<style lang="less" scoped>
.home {
    min-height: 100%;
    background:
        url('@/assets/img/<EMAIL>') no-repeat,
        #0e0821;
    background-size:
        375px 276.5px,
        100% 100%;
    background-position:
        0 0,
        0 0;
    position: relative;
    box-sizing: border-box;
}
</style>
