import { defineStore } from 'pinia';
import useChannelInfo from '../use-channel-info.js';
import API from '@/api';

const { uid, cid } = parseUrlQuery();
/**
 * @typedef {object} Init
 * @property {import('../../api/api.d.ts').InitResp} initData 初始化数据
 * @property {number} serverTime 服务器时间戳（秒）
 */

const useInitStore = defineStore('init', () => {
    /** @type {Init} */
    const stateInit = {
        initData: {},
        userInitData: {},
        serverTime: 0,
    };
    const hourRankRaceIdMap = {
        1: '领航赛区',
        2: '进阶赛区',
        3: '新星赛区',
    };

    const channelInfo = useChannelInfo();
    // 业务数据
    const stateApi = {
        // ...
    };
    const state = reactive(Object.assign({}, stateInit, stateApi));

    const isEnd = computed(() => {
        const { endTime } = state.initData;
        return state.serverTime >= endTime;
    });

    let countTimeKey = null;
    // 模拟服务器时间
    function countServerTime(serverTime) {
        if (countTimeKey) {
            clearInterval(countTimeKey);
            countTimeKey = null;
        }
        window.serverTime = serverTime;
        state.serverTime = serverTime;
        countTimeKey = setInterval(() => {
            window.serverTime += 1;
            state.serverTime += 1;
        }, 1000);
    }

    /**
     * @name init 数据初始化
     * @type {function(import('../../api/api.d.ts').InitReq)}
     */
    const init = async (payload = {}, config = {}) => {
        const [{ code, data }] = await API.init({ ...payload, uid: channelInfo.anchorUid || uid }, config);
        if (code === 0) {
            state.initData = data || {};
            countServerTime(data.serverTime);
        }
        return { code };
    };

    const userInit = async (payload = {}, config = {}) => {
        const [{ code, data }] = await API.init({ ...payload }, config);
        if (code === 0) {
            state.userInitData = data || {};
        }
        return { code };
    };

    const areaName = computed(() => hourRankRaceIdMap[state.initData.hourRankRaceId]);

    return {
        ...toRefs(state),
        init,
        userInit,
        isEnd,
        areaName,
    };
});

export default useInitStore;
