const API = {
    init() {
        return {
            serverTime: 1757418620,
            startTime: 1757418620,
            endTime: 1757418620,
            userInfo: {},
            isAnchor: 2,
            hourRankRaceId: 2,
            guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
            hasPermission: 2,
            sonGuildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
        };
    },
    getHourRank() {
        return {
            total: 1,
            list: [
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: false,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: false,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: false,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: false,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: false,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: false,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: false,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: false,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: false,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: false,
                },
            ],
            self: {
                userInfo: {},
                rank: 'Mock-rank',
                value: 1,
                prevDescribe: 'Mock-prevDescribe',
                nextDescribe: 'Mock-nextDescribe',
                point: 2,
                isReach: false,
            },
        };
    },
    getGuildRank() {
        return {
            total: 1,
            self: {
                guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
                value: 1,
                rank: 'Mock-rank',
                prevDescribe: 'Mock-prevDescribe',
                nextDescribe: 'Mock-nextDescribe',
                point: 1,
                mvpInfoList: {
                    tmpListData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
                    userInfo: {},
                },
            },
            list: [
                {
                    guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
                    value: 1,
                    rank: 'Mock-rank',
                    point: 1,
                    mvpInfoList: [
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                    ],
                },
                {
                    guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
                    value: 1,
                    rank: 'Mock-rank',
                    point: 1,
                    mvpInfoList: [
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                    ],
                },
                {
                    guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
                    value: 1,
                    rank: 'Mock-rank',
                    point: 1,
                    mvpInfoList: [
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                    ],
                },
                {
                    guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
                    value: 1,
                    rank: 'Mock-rank',
                    point: 1,
                    mvpInfoList: [
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                    ],
                },
                {
                    guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
                    value: 1,
                    rank: 'Mock-rank',
                    point: 1,
                    mvpInfoList: [
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                    ],
                },
                {
                    guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
                    value: 1,
                    rank: 'Mock-rank',
                    point: 1,
                    mvpInfoList: [
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                    ],
                },
                {
                    guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
                    value: 1,
                    rank: 'Mock-rank',
                    point: 1,
                    mvpInfoList: [
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                    ],
                },
                {
                    guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
                    value: 1,
                    rank: 'Mock-rank',
                    point: 1,
                    mvpInfoList: [
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                    ],
                },
                {
                    guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
                    value: 1,
                    rank: 'Mock-rank',
                    point: 1,
                    mvpInfoList: [
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                    ],
                },
                {
                    guildInfo: { guildId: 1, displayId: 1, name: 'Mock-name' },
                    value: 1,
                    rank: 'Mock-rank',
                    point: 1,
                    mvpInfoList: [
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                        { userInfo: {} },
                    ],
                },
            ],
        };
    },
    getGuildAnchorRank() {
        return {
            total: 2,
            list: [
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: true,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: true,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: true,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: true,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: true,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: true,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: true,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: true,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: true,
                },
                {
                    rank: 'Mock-rank',
                    value: 1,
                    userInfo: {},
                    channelInfo: {},
                    point: 1,
                    isReach: true,
                },
            ],
            self: {
                userInfo: {},
                rank: 'Mock-rank',
                value: 2,
                prevDescribe: 'Mock-prevDescribe',
                nextDescribe: 'Mock-nextDescribe',
                point: 2,
                isReach: true,
            },
        };
    },
    getMyGuildPoint() {
        return { point: 2, expireSoonPoint: 2 };
    },
    getPointRecords() {
        return {
            total: 1,
            list: [
                {
                    time: 1757418620,
                    pointsChange: 2,
                    opType: 2,
                    remark: 'Mock-remark',
                    userInfo: {},
                    prizeInfo: { prizeId: 2, count: 1, prizeName: 'Mock-prizeName', version: 1 },
                },
                {
                    time: 1757418620,
                    pointsChange: 2,
                    opType: 2,
                    remark: 'Mock-remark',
                    userInfo: {},
                    prizeInfo: { prizeId: 2, count: 1, prizeName: 'Mock-prizeName', version: 1 },
                },
                {
                    time: 1757418620,
                    pointsChange: 2,
                    opType: 2,
                    remark: 'Mock-remark',
                    userInfo: {},
                    prizeInfo: { prizeId: 2, count: 1, prizeName: 'Mock-prizeName', version: 1 },
                },
                {
                    time: 1757418620,
                    pointsChange: 2,
                    opType: 2,
                    remark: 'Mock-remark',
                    userInfo: {},
                    prizeInfo: { prizeId: 2, count: 1, prizeName: 'Mock-prizeName', version: 1 },
                },
                {
                    time: 1757418620,
                    pointsChange: 2,
                    opType: 2,
                    remark: 'Mock-remark',
                    userInfo: {},
                    prizeInfo: { prizeId: 2, count: 1, prizeName: 'Mock-prizeName', version: 1 },
                },
                {
                    time: 1757418620,
                    pointsChange: 2,
                    opType: 2,
                    remark: 'Mock-remark',
                    userInfo: {},
                    prizeInfo: { prizeId: 2, count: 1, prizeName: 'Mock-prizeName', version: 1 },
                },
                {
                    time: 1757418620,
                    pointsChange: 2,
                    opType: 2,
                    remark: 'Mock-remark',
                    userInfo: {},
                    prizeInfo: { prizeId: 2, count: 1, prizeName: 'Mock-prizeName', version: 1 },
                },
                {
                    time: 1757418620,
                    pointsChange: 2,
                    opType: 2,
                    remark: 'Mock-remark',
                    userInfo: {},
                    prizeInfo: { prizeId: 2, count: 1, prizeName: 'Mock-prizeName', version: 1 },
                },
                {
                    time: 1757418620,
                    pointsChange: 2,
                    opType: 2,
                    remark: 'Mock-remark',
                    userInfo: {},
                    prizeInfo: { prizeId: 2, count: 1, prizeName: 'Mock-prizeName', version: 1 },
                },
                {
                    time: 1757418620,
                    pointsChange: 2,
                    opType: 2,
                    remark: 'Mock-remark',
                    userInfo: {},
                    prizeInfo: { prizeId: 2, count: 1, prizeName: 'Mock-prizeName', version: 1 },
                },
            ],
        };
    },
    addMallGoods() {
        return {};
    },
    updateMallGoods() {
        return {};
    },
    deleteMallGoods() {
        return {};
    },
    pushMallGoods() {
        return {};
    },
    listMallGoods() {
        return {
            total: 1,
            list: [
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    displayWeight: 2,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    redemptionPoints: 2,
                    guildRedemptionLimit: 2,
                    siteRedemptionLimit: 1,
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                    version: 1,
                    status: 1,
                    remarks: 'Mock-remarks',
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    displayWeight: 2,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    redemptionPoints: 2,
                    guildRedemptionLimit: 2,
                    siteRedemptionLimit: 1,
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                    version: 1,
                    status: 1,
                    remarks: 'Mock-remarks',
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    displayWeight: 2,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    redemptionPoints: 2,
                    guildRedemptionLimit: 2,
                    siteRedemptionLimit: 1,
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                    version: 1,
                    status: 1,
                    remarks: 'Mock-remarks',
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    displayWeight: 2,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    redemptionPoints: 2,
                    guildRedemptionLimit: 2,
                    siteRedemptionLimit: 1,
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                    version: 1,
                    status: 1,
                    remarks: 'Mock-remarks',
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    displayWeight: 2,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    redemptionPoints: 2,
                    guildRedemptionLimit: 2,
                    siteRedemptionLimit: 1,
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                    version: 1,
                    status: 1,
                    remarks: 'Mock-remarks',
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    displayWeight: 2,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    redemptionPoints: 2,
                    guildRedemptionLimit: 2,
                    siteRedemptionLimit: 1,
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                    version: 1,
                    status: 1,
                    remarks: 'Mock-remarks',
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    displayWeight: 2,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    redemptionPoints: 2,
                    guildRedemptionLimit: 2,
                    siteRedemptionLimit: 1,
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                    version: 1,
                    status: 1,
                    remarks: 'Mock-remarks',
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    displayWeight: 2,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    redemptionPoints: 2,
                    guildRedemptionLimit: 2,
                    siteRedemptionLimit: 1,
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                    version: 1,
                    status: 1,
                    remarks: 'Mock-remarks',
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    displayWeight: 2,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    redemptionPoints: 2,
                    guildRedemptionLimit: 2,
                    siteRedemptionLimit: 1,
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                    version: 1,
                    status: 1,
                    remarks: 'Mock-remarks',
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    displayWeight: 2,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    redemptionPoints: 2,
                    guildRedemptionLimit: 2,
                    siteRedemptionLimit: 1,
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                    version: 1,
                    status: 1,
                    remarks: 'Mock-remarks',
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
            ],
        };
    },
    listRedeemGoods() {
        return {
            total: 1,
            list: [
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    redemptionPoints: 1,
                    guildRedemptionLimit: 2,
                    guildRedeemedCount: 2,
                    siteRedemptionLimit: 1,
                    siteRedeemedCount: 1,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    displayWeight: 1,
                    remarks: 'Mock-remarks',
                    version: 1,
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    redemptionPoints: 1,
                    guildRedemptionLimit: 2,
                    guildRedeemedCount: 2,
                    siteRedemptionLimit: 1,
                    siteRedeemedCount: 1,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    displayWeight: 1,
                    remarks: 'Mock-remarks',
                    version: 1,
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    redemptionPoints: 1,
                    guildRedemptionLimit: 2,
                    guildRedeemedCount: 2,
                    siteRedemptionLimit: 1,
                    siteRedeemedCount: 1,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    displayWeight: 1,
                    remarks: 'Mock-remarks',
                    version: 1,
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    redemptionPoints: 1,
                    guildRedemptionLimit: 2,
                    guildRedeemedCount: 2,
                    siteRedemptionLimit: 1,
                    siteRedeemedCount: 1,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    displayWeight: 1,
                    remarks: 'Mock-remarks',
                    version: 1,
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    redemptionPoints: 1,
                    guildRedemptionLimit: 2,
                    guildRedeemedCount: 2,
                    siteRedemptionLimit: 1,
                    siteRedeemedCount: 1,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    displayWeight: 1,
                    remarks: 'Mock-remarks',
                    version: 1,
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    redemptionPoints: 1,
                    guildRedemptionLimit: 2,
                    guildRedeemedCount: 2,
                    siteRedemptionLimit: 1,
                    siteRedeemedCount: 1,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    displayWeight: 1,
                    remarks: 'Mock-remarks',
                    version: 1,
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    redemptionPoints: 1,
                    guildRedemptionLimit: 2,
                    guildRedeemedCount: 2,
                    siteRedemptionLimit: 1,
                    siteRedeemedCount: 1,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    displayWeight: 1,
                    remarks: 'Mock-remarks',
                    version: 1,
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    redemptionPoints: 1,
                    guildRedemptionLimit: 2,
                    guildRedeemedCount: 2,
                    siteRedemptionLimit: 1,
                    siteRedeemedCount: 1,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    displayWeight: 1,
                    remarks: 'Mock-remarks',
                    version: 1,
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    redemptionPoints: 1,
                    guildRedemptionLimit: 2,
                    guildRedeemedCount: 2,
                    siteRedemptionLimit: 1,
                    siteRedeemedCount: 1,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    displayWeight: 1,
                    remarks: 'Mock-remarks',
                    version: 1,
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
                {
                    prizeId: 2,
                    prizeName: 'Mock-prizeName',
                    prizeImageUrl: 'Mock-prizeImageUrl',
                    redemptionPoints: 1,
                    guildRedemptionLimit: 2,
                    guildRedeemedCount: 2,
                    siteRedemptionLimit: 1,
                    siteRedeemedCount: 1,
                    displayStartTime: 1757418620,
                    displayEndTime: 1757418620,
                    displayWeight: 1,
                    remarks: 'Mock-remarks',
                    version: 1,
                    publishName: 'Mock-publishName',
                    publishTime: 1757418620,
                },
            ],
        };
    },
    grantGuildPoints() {
        return {};
    },
    deductGuildPoints() {
        return {};
    },
    listPointOperations() {
        return {
            total: 1,
            list: [
                {
                    id: 'Mock-id',
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    pointsChange: 2,
                    opType: 0,
                    remark: 'Mock-remark',
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                },
                {
                    id: 'Mock-id',
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    pointsChange: 2,
                    opType: 0,
                    remark: 'Mock-remark',
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                },
                {
                    id: 'Mock-id',
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    pointsChange: 2,
                    opType: 0,
                    remark: 'Mock-remark',
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                },
                {
                    id: 'Mock-id',
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    pointsChange: 2,
                    opType: 0,
                    remark: 'Mock-remark',
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                },
                {
                    id: 'Mock-id',
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    pointsChange: 2,
                    opType: 0,
                    remark: 'Mock-remark',
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                },
                {
                    id: 'Mock-id',
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    pointsChange: 2,
                    opType: 0,
                    remark: 'Mock-remark',
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                },
                {
                    id: 'Mock-id',
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    pointsChange: 2,
                    opType: 0,
                    remark: 'Mock-remark',
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                },
                {
                    id: 'Mock-id',
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    pointsChange: 2,
                    opType: 0,
                    remark: 'Mock-remark',
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                },
                {
                    id: 'Mock-id',
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    pointsChange: 2,
                    opType: 0,
                    remark: 'Mock-remark',
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                },
                {
                    id: 'Mock-id',
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    pointsChange: 2,
                    opType: 0,
                    remark: 'Mock-remark',
                    operatorName: 'Mock-operatorName',
                    operateTime: 1757418620,
                },
            ],
        };
    },
    exportPointOperations() {
        return { url: 'Mock-url' };
    },
    listGuildPointInventory() {
        return {
            total: 2,
            list: [
                {
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    remainPoints: 2,
                },
                {
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    remainPoints: 2,
                },
                {
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    remainPoints: 2,
                },
                {
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    remainPoints: 2,
                },
                {
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    remainPoints: 2,
                },
                {
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    remainPoints: 2,
                },
                {
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    remainPoints: 2,
                },
                {
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    remainPoints: 2,
                },
                {
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    remainPoints: 2,
                },
                {
                    guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' },
                    ownerTtid: 2,
                    ownerNickname: 'Mock-ownerNickname',
                    remainPoints: 2,
                },
            ],
        };
    },
    exportGuildPointInventory() {
        return { url: 'Mock-url' };
    },
    getGuildInfoByTtid() {
        return { guildInfo: { guildId: 2, displayId: 1, name: 'Mock-name' }, userInfo: {} };
    },
    getMallGoodsPushLog() {
        return {
            total: 1,
            list: [
                { publishName: 'Mock-publishName', publishTime: 1757418620 },
                { publishName: 'Mock-publishName', publishTime: 1757418620 },
                { publishName: 'Mock-publishName', publishTime: 1757418620 },
                { publishName: 'Mock-publishName', publishTime: 1757418620 },
                { publishName: 'Mock-publishName', publishTime: 1757418620 },
                { publishName: 'Mock-publishName', publishTime: 1757418620 },
                { publishName: 'Mock-publishName', publishTime: 1757418620 },
                { publishName: 'Mock-publishName', publishTime: 1757418620 },
                { publishName: 'Mock-publishName', publishTime: 1757418620 },
                { publishName: 'Mock-publishName', publishTime: 1757418620 },
            ],
        };
    },
    previewBatchGrantGuildPoints() {
        return {
            list: [
                { errorMsg: 'Mock-errorMsg', index: 1 },
                { errorMsg: 'Mock-errorMsg', index: 1 },
                { errorMsg: 'Mock-errorMsg', index: 1 },
                { errorMsg: 'Mock-errorMsg', index: 1 },
                { errorMsg: 'Mock-errorMsg', index: 1 },
                { errorMsg: 'Mock-errorMsg', index: 1 },
                { errorMsg: 'Mock-errorMsg', index: 1 },
                { errorMsg: 'Mock-errorMsg', index: 1 },
                { errorMsg: 'Mock-errorMsg', index: 1 },
                { errorMsg: 'Mock-errorMsg', index: 1 },
            ],
        };
    },
    batchGrantGuildPoints() {
        return {};
    },
    previewBatchDeductGuildPoints() {
        return {
            list: [
                { errorMsg: 'Mock-errorMsg', index: 2 },
                { errorMsg: 'Mock-errorMsg', index: 2 },
                { errorMsg: 'Mock-errorMsg', index: 2 },
                { errorMsg: 'Mock-errorMsg', index: 2 },
                { errorMsg: 'Mock-errorMsg', index: 2 },
                { errorMsg: 'Mock-errorMsg', index: 2 },
                { errorMsg: 'Mock-errorMsg', index: 2 },
                { errorMsg: 'Mock-errorMsg', index: 2 },
                { errorMsg: 'Mock-errorMsg', index: 2 },
                { errorMsg: 'Mock-errorMsg', index: 2 },
            ],
        };
    },
    batchDeductGuildPoints() {
        return {};
    },
};
const getMockData = (type, payload) =>
    new Promise((resolve) => {
        const delay = Math.round(Math.random() * 10) * 50; // 模拟访问延迟
        let data;
        if (typeof API[type] === 'function')
            data = API[type](payload);
        else data = API[type];

        // eslint-disable-next-line no-console
        console.log(
            `模拟接口请求名称<=== ${type} delay: ${delay} ms; payload: ${JSON.stringify(payload)}`,
        );
        window.setTimeout(() => {
            // eslint-disable-next-line no-console
            console.log('模拟接口请求返回===>', data);
            resolve({
                code: 0,
                data,
                msg: '',
            });
        }, delay);
    });
export default getMockData;
