<template>
    <div>
        <div class="nav">
            <div
                v-for="item of NAV"
                :key="item.value"
                class="nav-item relative"
                :class="{ 'bg-active': current === item.value }"
                @click="navStore.changeNav(item.value)"
            >
                <img
                    class="object-contain"
                    :src="getNavImg(item)"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { NAV } from './const';
import useNav from './use-nav';

const navStore = useNav();

const { current } = storeToRefs(navStore);

const getNavImg = item => (current.value === item?.value ? item.activeImg : item.inactiveImg);
</script>

<style lang="less" scoped>
.nav {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    width: 100vw;
    box-sizing: border-box;
    left: 0;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 0px;
    overflow: hidden;
    &-item {
        width: 187.5px;
        height: 57px;

        img {
            height: 100%;
            width: 100%;
        }
    }
}
</style>
