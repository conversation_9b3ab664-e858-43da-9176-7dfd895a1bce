// 执行pb.cjs命令后会覆盖掉手写的代码，请谨慎操作!
import request from '../utils/request';
import getMockData from './mockData';

export const fetchApi = ({ proPrefix = '/activity.Activity/', api, data = {}, config = {} }) => {
    const { mock } = myWebview.params;
    if (mock)
        return to(getMockData(api, data));

    const url = `${proPrefix}/${api}`.replace('//', '/');
    return to(request.post(url, data, config));
};

/**
 * ********************************
 * *********  活动接口 *************
 * ********************************
 */
const REQUEST_API_MAP = {
    init: 'init',
    getHourRank: 'getHourRank',
    getGuildRank: 'getGuildRank',
    getGuildAnchorRank: 'getGuildAnchorRank',
    getMyGuildPoint: 'getMyGuildPoint',
    getPointRecords: 'getPointRecords',
    addMallGoods: 'addMallGoods',
    updateMallGoods: 'updateMallGoods',
    deleteMallGoods: 'deleteMallGoods',
    pushMallGoods: 'pushMallGoods',
    listMallGoods: 'listMallGoods',
    listRedeemGoods: 'listRedeemGoods',
    grantGuildPoints: 'grantGuildPoints',
    deductGuildPoints: 'deductGuildPoints',
    listPointOperations: 'listPointOperations',
    exportPointOperations: 'exportPointOperations',
    listGuildPointInventory: 'listGuildPointInventory',
    exportGuildPointInventory: 'exportGuildPointInventory',
    getGuildInfoByTtid: 'getGuildInfoByTtid',
    getMallGoodsPushLog: 'getMallGoodsPushLog',
    previewBatchGrantGuildPoints: 'previewBatchGrantGuildPoints',
    batchGrantGuildPoints: 'batchGrantGuildPoints',
    previewBatchDeductGuildPoints: 'previewBatchDeductGuildPoints',
    batchDeductGuildPoints: 'batchDeductGuildPoints',
};

/** @type {function(import('./api.d.ts').userReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').initRes},any]>} */
export const init = (data, config) => fetchApi({ api: REQUEST_API_MAP.init, data, config });

/** @type {function(import('./api.d.ts').getHourRankReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getHourRankRes},any]>} */
export const getHourRank = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getHourRank, data, config });

/** @type {function(import('./api.d.ts').getGuildRankReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getGuildRankRes},any]>} */
export const getGuildRank = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getGuildRank, data, config });

/** @type {function(import('./api.d.ts').getGuildAnchorRankReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getHourRankRes},any]>} */
export const getGuildAnchorRank = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getGuildAnchorRank, data, config });

/** @type {function(import('./api.d.ts').userReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getMyGuildPointRes},any]>} */
export const getMyGuildPoint = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getMyGuildPoint, data, config });

/** @type {function(import('./api.d.ts').getPointRecordsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getPointRecordsRes},any]>} */
export const getPointRecords = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getPointRecords, data, config });

/** @type {function(import('./api.d.ts').addOrUpdateMallGoodsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').Empty},any]>} */
export const addMallGoods = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.addMallGoods, data, config });

/** @type {function(import('./api.d.ts').addOrUpdateMallGoodsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').Empty},any]>} */
export const updateMallGoods = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.updateMallGoods, data, config });

/** @type {function(import('./api.d.ts').deleteMallGoodsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').Empty},any]>} */
export const deleteMallGoods = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.deleteMallGoods, data, config });

/** @type {function(import('./api.d.ts').pushMallGoodsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').Empty},any]>} */
export const pushMallGoods = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.pushMallGoods, data, config });

/** @type {function(import('./api.d.ts').listMallGoodsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').listMallGoodsRes},any]>} */
export const listMallGoods = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.listMallGoods, data, config });

/** @type {function(import('./api.d.ts').listRedeemGoodsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').listRedeemGoodsRes},any]>} */
export const listRedeemGoods = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.listRedeemGoods, data, config });

/** @type {function(import('./api.d.ts').grantGuildPointsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').Empty},any]>} */
export const grantGuildPoints = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.grantGuildPoints, data, config });

/** @type {function(import('./api.d.ts').deductGuildPointsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').Empty},any]>} */
export const deductGuildPoints = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.deductGuildPoints, data, config });

/** @type {function(import('./api.d.ts').listPointOperationsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').listPointOperationsRes},any]>} */
export const listPointOperations = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.listPointOperations, data, config });

/** @type {function(import('./api.d.ts').listPointOperationsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').exportPointOperationsRes},any]>} */
export const exportPointOperations = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.exportPointOperations, data, config });

/** @type {function(import('./api.d.ts').listGuildPointInventoryReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').listGuildPointInventoryRes},any]>} */
export const listGuildPointInventory = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.listGuildPointInventory, data, config });

/** @type {function(import('./api.d.ts').listGuildPointInventoryReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').exportGuildPointInventoryRes},any]>} */
export const exportGuildPointInventory = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.exportGuildPointInventory, data, config });

/** @type {function(import('./api.d.ts').getGuildInfoByTtidReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getGuildInfoByTtidRes},any]>} */
export const getGuildInfoByTtid = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getGuildInfoByTtid, data, config });

/** @type {function(import('./api.d.ts').Empty):Promise<[{code:number,msg:string,data:import('./api.d.ts').getMallGoodsPushLogRes},any]>} */
export const getMallGoodsPushLog = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getMallGoodsPushLog, data, config });

/** @type {function(import('./api.d.ts').batchGrantGuildPointsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').previewBatchGrantGuildPointsRes},any]>} */
export const previewBatchGrantGuildPoints = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.previewBatchGrantGuildPoints, data, config });

/** @type {function(import('./api.d.ts').batchGrantGuildPointsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').Empty},any]>} */
export const batchGrantGuildPoints = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.batchGrantGuildPoints, data, config });

/** @type {function(import('./api.d.ts').batchDeductGuildPointsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').previewBatchDeductGuildPointsRes},any]>} */
export const previewBatchDeductGuildPoints = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.previewBatchDeductGuildPoints, data, config });

/** @type {function(import('./api.d.ts').batchDeductGuildPointsReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').Empty},any]>} */
export const batchDeductGuildPoints = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.batchDeductGuildPoints, data, config });

export default {
    init,
    getHourRank,
    getGuildRank,
    getGuildAnchorRank,
    getMyGuildPoint,
    getPointRecords,
    addMallGoods,
    updateMallGoods,
    deleteMallGoods,
    pushMallGoods,
    listMallGoods,
    listRedeemGoods,
    grantGuildPoints,
    deductGuildPoints,
    listPointOperations,
    exportPointOperations,
    listGuildPointInventory,
    exportGuildPointInventory,
    getGuildInfoByTtid,
    getMallGoodsPushLog,
    previewBatchGrantGuildPoints,
    batchGrantGuildPoints,
    previewBatchDeductGuildPoints,
    batchDeductGuildPoints,
};
