import { defineStore } from 'pinia';
import { computed, reactive, ref } from 'vue';
import { getCurrentChannelInfo } from '@/utils/jsbridge';

const { uid, cid } = parseUrlQuery();

const useChannelInfo = defineStore('channelInfo', () => {
    const info = ref({});

    const update = () => {
        let anchorInfo = getCurrentChannelInfo();

        try {
            if (typeof anchorInfo === 'string') {
                anchorInfo = JSON.parse(anchorInfo);
            }
        }
        catch (error) {
            anchorInfo = null;
        }
        console.log('房间信息：', anchorInfo);
        // anchorInfo = {
        //     type: 7,
        //     creator_uid: 2415866,
        // };
        if (cid) {
            anchorInfo = {
                type: 7,
                creator_uid: cid,
            };
        }
        if (anchorInfo?.type !== 7) {
            return;
        }

        info.value = anchorInfo;
        console.log('房主：', info.value?.creator_uid);
        console.log('房间类型：', info.value?.type);
    };

    const isInRoom = computed(() => info.value?.type === 7); // 只在听听房间中
    const isInMyRoom = computed(() => !!(info.value?.type && info.value?.creator_uid === uid)); // 我是达人，且在我的房间
    const isNotInMyRoom = computed(() => !isInMyRoom.value && isInRoom.value);

    const anchorUid = computed(() => info.value?.creator_uid ?? undefined);

    update(); // 先更新一下

    return {
        info,
        isInRoom,
        isInMyRoom,
        isNotInMyRoom,
        anchorUid,
        update,
    };
});

export default useChannelInfo;
