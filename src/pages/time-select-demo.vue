<!-- src/pages/time-select-demo.vue -->
<template>
    <page-container>
        <div class="time-select-demo">
            <h1 class="title">时间段选择组件演示</h1>

            <!-- 时间选择组件 -->
            <time-select
                :auto-init="true"
                @date-change="handleDateChange"
                @time-change="handleTimeChange" />

            <!-- 当前选择信息显示 -->
            <div class="selection-info">
                <h2>当前选择：</h2>
                <p><strong>日期：</strong>{{ selectedDate || '未选择' }}</p>
                <p><strong>时间段：</strong>{{ selectedTimeText || '未选择' }}</p>
                <p><strong>是否今天：</strong>{{ timeStore.isToday ? '是' : '否' }}</p>
            </div>

            <!-- 调试信息 -->
            <div class="debug-info">
                <h2>调试信息：</h2>
                <p><strong>服务器时间：</strong>{{ serverTimeText }}</p>
                <p><strong>活动状态：</strong>{{ activityStatus }}</p>
                <p><strong>可选日期数量：</strong>{{ timeStore.dates.length }}</p>
                <p><strong>当前时间段索引：</strong>{{ timeStore.getCurrentTimeRangeIndex() }}</p>
            </div>

            <!-- 操作按钮 -->
            <div class="actions">
                <button
                    class="action-btn"
                    @click="handleReset">
                    重置选择
                </button>
                <button
                    class="action-btn"
                    @click="handleInit">
                    重新初始化
                </button>
            </div>
        </div>
    </page-container>
</template>

<script setup>
import TimeSelect from '@/components/tab1/time-select.vue';
import useTimeSelect from '@/stores/use-time-select-store';
import useActivityTime from '@/stores/modules/use-activity-time';
import dayjs from '@/utils/dayjs';

// Store
const timeStore = useTimeSelect();
const activityTimeStore = useActivityTime();

// 响应式数据
const selectedDate = ref('');
const selectedTime = ref('');

// 计算属性
const selectedTimeText = computed(() => {
    if (!selectedTime.value)
        return '';
    const timeRange = timeStore.timeRanges.find(item => item.value === selectedTime.value);
    return timeRange?.rangeStr || '';
});

const serverTimeText = computed(() => {
    if (!activityTimeStore.serverTime)
        return '暂无';
    return dayjs.unix(activityTimeStore.serverTime).format('YYYY-MM-DD HH:mm:ss');
});

const activityStatus = computed(() => {
    if (activityTimeStore.notStarted)
        return '未开始';
    if (activityTimeStore.isEnd)
        return '已结束';
    if (activityTimeStore.isInActivityDate)
        return '进行中';
    return '未知';
});

// 方法
function handleDateChange(date) {
    selectedDate.value = date;
    console.log('日期变更:', date);
}

function handleTimeChange(time) {
    selectedTime.value = time;
    console.log('时间段变更:', time);
}

function handleReset() {
    timeStore.resetNull();
    selectedDate.value = '';
    selectedTime.value = '';
    showToast('已重置选择');
}

function handleInit() {
    timeStore.init();
    showToast('重新初始化完成');
}

// 生命周期
onMounted(() => {
    console.log('时间选择演示页面已加载');
});

// 监听 store 中的选择变化
watch(() => timeStore.selectedDate, (newDate) => {
    selectedDate.value = newDate;
});

watch(() => timeStore.selectedTimeRange, (newTime) => {
    selectedTime.value = newTime;
});
</script>

<style lang="less" scoped>
.time-select-demo {
    padding: 20px;
    max-width: 375px;
    margin: 0 auto;

    .title {
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 20px;
        color: #333;
    }

    .selection-info,
    .debug-info {
        margin-top: 20px;
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 8px;

        h2 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #333;
        }

        p {
            margin: 8px 0;
            font-size: 14px;
            color: #666;

            strong {
                color: #333;
            }
        }
    }

    .actions {
        margin-top: 20px;
        display: flex;
        gap: 12px;
        justify-content: center;

        .action-btn {
            padding: 10px 20px;
            border: 1px solid #007aff;
            border-radius: 6px;
            background-color: #007aff;
            color: #fff;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
                background-color: #0056cc;
                border-color: #0056cc;
            }

            &:active {
                background-color: #004499;
            }
        }
    }
}
</style>
