// src/stores/use-time-select-store.js
import isSameOrAfter from 'dayjs/esm/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/esm/plugin/isSameOrBefore';
import { computed, onUnmounted, ref, unref, watch } from 'vue';
import { defineStore } from 'pinia';
import dayjs from '@/utils/dayjs';
import useActivityTime from '@/stores/modules/use-activity-time';
import resolveUnref from '@/use/resolve-unref';
import { checkInTargetDateTimeRange } from '@/utils/day-utils';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

const TOTAL = '';

const DEFAULT_OPTIONS = {
    dateTemplate: 'MM月DD日', // 日期格式
    autoSelect: false, // 初始化的时候是否自动选中
};

// 生成时间范围
const genTimeRange = (start, end) => [start.format('HH'), end.format('HH')];

/**
 * 整合日期和时间段选择的 store
 */
const useTimeSelect = defineStore('timeSelect', () => {
    const options = { ...DEFAULT_OPTIONS };
    const { dateTemplate, autoSelect } = options;

    const activityTimeStore = useActivityTime();

    // 日期选择相关
    const selectedDate = ref(''); // 选中的日期 YYYY-MM-DD 格式
    // 时间段选择相关
    const selectedTimeRange = ref(''); // 选中的时间段 HH:mm:ss 格式

    /**
     * 所有的日期
     */
    const dates = computed(() =>
        activityTimeStore.activityDates.map(date => ({
            date,
            value: date.format('YYYY-MM-DD'),
            dateStr: date.format(dateTemplate),
        })),
    );

    /**
     * 6个时间段：18-19、19-20、20-21、21-22、22-23、23-24
     */
    const timeRanges = computed(() => {
        const hours = [18, 19, 20, 21, 22, 23];
        return hours.map((h) => {
            const start = dayjs().hour(h).minute(0).second(0).millisecond(0);
            const end = start.clone().add(1, 'hour');
            const range = genTimeRange(start, end);
            const rangeStr = `${range[0]}-${range[1]}点`;
            return {
                range,
                value: start.format('HH:mm:ss'),
                rangeStr,
                hour: h,
            };
        });
    });

    /**
     * 当前时间所处的时间范围（基于服务器时间当天）
     */
    const currentTimeRange = computed(() => {
        const { serverTime } = activityTimeStore;
        if (!serverTime)
            return null;
        const rawTimeRanges = resolveUnref(timeRanges);
        const target = rawTimeRanges.find(item => checkInTargetDateTimeRange(dayjs.unix(serverTime), ...item.range));
        return target || null;
    });

    /**
     * 检查是否是有效日期
     * @param {*} date
     * @returns
     */
    const checkAvailableDate = (date) => {
        const { serverTime } = activityTimeStore;
        if (!serverTime)
            return false;
        const targetDateBegin = dayjs(date, 'YYYY-MM-DD', 'Asia/Shanghai');
        const currentDateEnd = dayjs.unix(serverTime);
        console.log(date, targetDateBegin, currentDateEnd);

        return targetDateBegin.isSameOrBefore(currentDateEnd); // 选择的目标时间是否在当前天最后一刻之前
    };

    const selectTotal = () => {
        selectedDate.value = TOTAL;
    };

    const isToday = computed(() => selectedDate.value === dayjs.unix(activityTimeStore.serverTime).format('YYYY-MM-DD'));

    /**
     * 选择时间段
     * @param {*} val 时间段值 HH:mm:ss 格式
     * @returns
     */
    const changeTimeRange = (val) => {
        // 未开启时间处理：基于选中日期 + 选中小时
        const dateStr = selectedDate.value;
        // 判断选中的是不是今天
        const isToday = dayjs.unix(activityTimeStore.serverTime).format('YYYY-MM-DD') === dateStr;

        if (!activityTimeStore.isInStartDay) {
            showToast('该时段暂未开启~');
            return;
        }

        if (
            isToday
            && dayjs
                .unix(activityTimeStore.serverTime)
                .isBefore(dayjs(`${dateStr} ${val}`))
        ) {
            showToast('该时段暂未开启~');
            return;
        }
        selectedTimeRange.value = val;
    };

    /**
     * 获取当前时间段在数组中的索引
     */
    const getCurrentTimeRangeIndex = () => {
        const rawTimeRanges = resolveUnref(timeRanges);
        const currentIndex = rawTimeRanges.findIndex(item => item.value === selectedTimeRange.value);
        return currentIndex >= 0 ? currentIndex : 0;
    };

    /**
     * 切换到上一个时间段
     */
    const prevTimeRange = () => {
        const rawTimeRanges = resolveUnref(timeRanges);
        const currentIndex = getCurrentTimeRangeIndex();

        if (currentIndex <= 0) {
            showToast('没有更多了~');
            return;
        }

        const prevRange = rawTimeRanges[currentIndex - 1];
        changeTimeRange(prevRange.value);
    };

    /**
     * 切换到下一个时间段
     */
    const nextTimeRange = () => {
        const rawTimeRanges = resolveUnref(timeRanges);
        const currentIndex = getCurrentTimeRangeIndex();

        if (currentIndex >= rawTimeRanges.length - 1) {
            showToast('没有更多了~');
            return;
        }

        const nextRange = rawTimeRanges[currentIndex + 1];
        changeTimeRange(nextRange.value);
    };

    // 活动是否尚未到最早可选时段（以可选日期中的第一天 18:00 为准）
    const isBeforeTimeRange = computed(() => {
        const { serverTime } = activityTimeStore;
        const allDates = dates.value;
        if (!serverTime || !allDates?.length)
            return true;
        const firstDate = allDates[0]?.value || dayjs.unix(serverTime).format('YYYY-MM-DD');
        return dayjs
            .unix(serverTime)
            .isBefore(dayjs(`${firstDate} 18:00:00`));
    });

    const initTimeRange = () => {
        const rawTimeRanges = resolveUnref(timeRanges);
        if (!rawTimeRanges?.length)
            return;

        if (isToday.value) {
            const now = dayjs.unix(activityTimeStore.serverTime);
            const h = now.hour();
            // 若 <18: 暂未开始，不选择
            if (h < 18)
                return;
            // 找到当前时间对应的时间段
            const currentRange = rawTimeRanges.find(item => h >= item.hour && h < item.hour + 1);
            if (currentRange) {
                changeTimeRange(currentRange.value);
                return;
            }
            // 如果没找到对应时间段，默认选择最后一个
            changeTimeRange(rawTimeRanges[rawTimeRanges.length - 1]?.value);
            return;
        }
        // 非今日，且不在最早时间之前，默认选择最后一个时间段
        if (!isBeforeTimeRange.value) {
            const lastTimeRangeVal = rawTimeRanges[rawTimeRanges.length - 1]?.value;
            changeTimeRange(lastTimeRangeVal);
        }
    };
    /**
     * 选择日期
     * 非有效日期弹 toast，不做日期切换
     * @param {*} date
     * @returns
     */
    const selectDate = (date = '') => {
        if (!date || date === TOTAL) {
            selectTotal();
            return;
        }
        if (!dates.value.find(item => item.value === date)) {
            return;
        }
        const { serverTime } = activityTimeStore;
        // 若选择"今天"，且当前时间未到 18 点，则提示未开始
        const todayTime = dayjs.unix(serverTime);
        const todayStr = todayTime.format('YYYY-MM-DD');
        const curHour = todayTime.hour();
        if (date === todayStr && curHour < 18) {
            showToast('暂未开始');
            return;
        }
        // 其他未来日期统一按不可选处理
        if (!checkAvailableDate(date)) {
            showToast('暂未开始');
            return;
        }
        selectedDate.value = date;
        // 选择日期成功后，初始化时间段定位
        initTimeRange();
    };
    const init = () => {
        const rawDates = unref(dates);
        if (!activityTimeStore.isInActivityDate) {
            if (activityTimeStore.isEnd && rawDates?.length) {
                selectDate(rawDates[rawDates.length - 1].value);
            }
            return;
        }
        const curHour = dayjs.unix(activityTimeStore.serverTime).hour();
        console.log('当前时间：', curHour);
        // 当前未到18点，使用前一天的时间
        if (curHour < 18) {
            console.log('当前未到18点，使用前一天的时间');
            const targetDateStr = dayjs.unix(activityTimeStore.serverTime).subtract(1, 'day').format('YYYY-MM-DD');
            selectDate(targetDateStr);
            return;
        }
        const targetDateStr = dayjs.unix(activityTimeStore.serverTime).format('YYYY-MM-DD');
        selectDate(targetDateStr);
    };

    const reset = () => init();

    const resetNull = () => {
        selectedDate.value = null;
        selectedTimeRange.value = null;
    };

    if (autoSelect) {
        // 自动选择
        const stopWatch = watch(
            () => dates.value,
            (allDates) => {
                if (!allDates?.length) {
                    return;
                }
                init();
            },
            {
                immediate: true,
            },
        );

        onUnmounted(() => {
            stopWatch();
        });
    }

    return {
        totalKey: TOTAL,
        dates,
        timeRanges,
        currentTimeRange,
        selectedDate,
        selectedTimeRange,
        reset,
        selectTotal,
        selectDate,
        changeTimeRange,
        prevTimeRange,
        nextTimeRange,
        getCurrentTimeRangeIndex,
        init,
        initTimeRange,
        isToday,
        resetNull,
        isBeforeTimeRange,
    };
});

export default useTimeSelect;
