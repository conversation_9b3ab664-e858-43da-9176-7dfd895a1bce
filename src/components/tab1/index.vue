<template>
    <div class="flex flex-col items-center">
        <van-list
            :finished="!rankStore.hasMore"
            offset="30"
            class="w-full"
            @load="handleLoadMore"
        >
            <div
                v-if="initStore.areaName"
                class="bg-default h-[41px] w-[240.5px] flex items-center justify-center"
                :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
            >
                本活动中你的所属赛区: {{ initStore.areaName || '' }}
            </div>

            <div
                class="bg-default mt-8 h-[116px] w-[343px] flex items-center justify-center"
                :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
            >
                <div
                    v-for="item in rewardList"
                    class="bg-default mx-[10px] flex flex-shrink-0 flex-col items-center">
                    <div
                        class="bg-default flex-center flex-center h-[72px] w-[72px] flex"
                        :style="`background-image: url(${requireImg('<EMAIL>')})`">
                        <img
                            :src="item.imageUrl"
                            class="h-80% w-80% object-contain"
                            alt="">
                    </div>
                    <div class="mt-[6px] text-[12px] text-[#793F00] leading-[12px]">{{ item.name }}{{ item.mark === '包裹' ? `${item.price}豆` : item.mark || '' }}</div>
                </div>
            </div>
            <div
                class="bg-default rank-list relative mt-19 flex flex-col items-center"
                :class="[rankStore.hasSelfRank ? 'pb-110' : '']"
            >
                <rank-time-selector></rank-time-selector>
                <div class="text-mine mt-53 text-[11px] text-[#4B00D6]">
                    骑士团成员为守护达人送出骑士团礼物(含开通/续费)送礼值
                </div>

                <!-- 错误状态 -->
                <div
                    v-if="rankStore.error.hasError"
                    class="error-state">
                    <div class="error-icon">⚠️</div>
                    <div class="error-message">{{ rankStore.error.message }}</div>
                    <div
                        v-if="rankStore.error.canRetry"
                        class="retry-btn"
                        @click="handleRetry"
                    >
                        重新加载
                    </div>
                </div>

                <!-- 正常内容 -->
                <template v-else>
                    <!-- 普通排名列表 -->
                    <div
                        v-if="rankStore.list.length > 0"
                        class="normal-list">
                        <rank-item
                            v-for="(item, index) in rankStore.list"
                            :key="`${rankStore.currentType}-${item.uid || item.id}-${index}`"
                            :item="item"
                            :rank="index + 4"
                            :type="rankStore.currentType"
                            :is-anchor="false"
                        />
                    </div>

                    <!-- 自己的排名 -->
                    <div
                        v-if="rankStore.hasSelfRank"
                        class="self-rank">
                        <rank-item
                            :is-anchor="false"
                            :item="rankStore.selfRank"
                            :rank="rankStore.selfRank.rank || rankStore.selfRank.ranking"
                            :is-self="true"
                        />
                    </div>

                    <!-- 空状态 -->
                    <div
                        v-if="rankStore.showEmptyState"
                        class="empty-state">
                        <div class="empty-text">暂无榜单数据</div>
                    </div>

                    <!-- 加载更多提示 -->
                    <div
                        v-if="rankStore.loading.more"
                        class="loading-more">
                        <div class="loading-spinner"></div>
                        <span>加载中...</span>
                    </div>

                    <!-- 没有更多数据 -->
                    <div
                        v-if="!rankStore.hasMore && rankStore.list.length > 0"
                        class="no-more">
                        <div class="no-more-line"></div>
                        <span>没有更多数据了</span>
                        <div class="no-more-line"></div>
                    </div>
                </template>
            </div>
        </van-list>
    </div>
</template>

<script setup>
import dayjs from 'dayjs';
import useRankStore from './use-rank-store';
import { getRewardInfo, normalizeUserData } from '@/auto-imports/my-utils';
import useLoading from '@/hooks/use-loading';
import useInitStore from '@/stores/modules/use-init-store';

const initStore = useInitStore();

const rankStore = useRankStore();
useLoading(computed(() => rankStore.loading));

const rewardList = [
    { ...getRewardInfo('C1'), name: '' },
    { ...getRewardInfo('D1') },
    { ...getRewardInfo('E1') },
];

// 加载更多
const handleLoadMore = async () => {
    if (rankStore.loading || !rankStore.hasMore) {
        return;
    }
    try {
        await rankStore.loadMore();
    }
    catch {

    }
};

onMounted(async () => {
    rankStore.retryQuery();
});
</script>

<style lang="less" scoped>
.rank-list {
    width: 100%;
    height: 100%;
    .point-9-px(url('@/assets/img/<EMAIL>'),375, 610, 750, 0, 1, 0,2);
    border-top: 0;
    border-bottom: 0;
    border-left: 0;
    border-right: 0;
    height: auto;
    min-height: 610px;
    width: 100%;
}
</style>
