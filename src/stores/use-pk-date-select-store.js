import isSameOrAfter from 'dayjs/esm/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/esm/plugin/isSameOrBefore';
import { computed, onUnmounted, ref, unref, watch } from 'vue';
import { defineStore } from 'pinia';
import dayjs from '@/utils/dayjs';
import useActivityTime from '@/store/use-activity-time';
import { dayjsToDayEnd, showToast } from '@/utils/utils';
import usePkTimeSelect from '@/store/use-pk-time-select';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

const TOTAL = '';

const DEFAULT_OPTIONS = {
    dateTemplate: 'YYYY-MM-DD', // 日期格式
    autoSelect: false, // 初始化的时候是否自动选中
};

const useDateSelect = defineStore('pkDateSelect', () => {
    const options = { ...DEFAULT_OPTIONS };
    const { dateTemplate, autoSelect } = options;

    const activityTimeStore = useActivityTime();

    const selected = ref(''); // 选中的时间 YYYY-MM-DD 格式

    /**
     * 所有的日期
     */
    const dates = computed(() =>
        activityTimeStore.activityDates.slice(1).map((date, index) => ({
            date,
            value: date.format('YYYY-MM-DD'),
            dateStr: date.format(dateTemplate),
        })),
    );

    /**
     * 检查是否是有效日期
     * @param {*} date
     * @returns
     */
    const checkAvaliableDate = (date) => {
        const { serverTime } = activityTimeStore;
        if (!serverTime)
            return false;
        const targetDateBegin = dayjs(date, 'YYYY-MM-DD', 'Asia/Shanghai');
        const currentDateEnd = dayjs.unix(serverTime);
        console.log(date, targetDateBegin, currentDateEnd);

        return targetDateBegin.isSameOrBefore(currentDateEnd); // 选择的目标时间是否在当前天最后一刻之前
    };

    const selectTotal = () => {
        selected.value = TOTAL;
    };

    /**
     * 选择日期
     * 非有效日期弹 toast，不做日期切换
     * @param {*} date
     * @returns
     */
    const selectDate = (date = '') => {
        if (!date || date === TOTAL) {
            selectTotal();
            return;
        }
        if (!dates.value.find(item => item.value === date)) {
            return;
        }
        const { serverTime } = activityTimeStore;
        // 若选择“今天”，且当前时间未到 19 点，则提示未开始
        const todayTime = dayjs.unix(serverTime);
        const todayStr = todayTime.format('YYYY-MM-DD');
        const curHour = todayTime.hour();
        if (date === todayStr && curHour < 19) {
            showToast('暂未开始');
            return;
        }
        // 其他未来日期统一按不可选处理
        if (!checkAvaliableDate(date)) {
            showToast('暂未开始');
            return;
        }
        selected.value = date;
        // 选择日期成功后，初始化小时段定位
        try {
            const pkTimeStore = usePkTimeSelect();
            pkTimeStore.init();
        }
        catch (e) {
            // 忽略异常，避免影响选择
        }
    };

    const init = () => {
        const rawDates = unref(dates);
        if (!activityTimeStore.isInActivityDate) {
            if (activityTimeStore.isEnd && rawDates?.length) {
                selectDate(rawDates[rawDates.length - 1].value);
            }
            return;
        }
        const curHour = dayjs.unix(activityTimeStore.serverTime).hour();
        console.log('当前时间：', curHour);
        // 当前未到19点，使用前一天的时间
        if (curHour < 19) {
            console.log('当前未到19点，使用前一天的时间');
            const targetDateStr = dayjs.unix(activityTimeStore.serverTime).subtract(1, 'day').format('YYYY-MM-DD');
            selectDate(targetDateStr);
            return;
        }
        const targetDateStr = dayjs.unix(activityTimeStore.serverTime).format('YYYY-MM-DD');
        selectDate(targetDateStr);
    };

    const reset = () => init();

    const resetNull = () => {
        selected.value = null;
    };

    if (autoSelect) {
        // 自动选择
        const stopWatch = watch(
            () => dates.value,
            (allDates) => {
                if (!allDates?.length) {
                    return;
                }
                init();
            },
            {
                immediate: true,
            },
        );

        onUnmounted(() => {
            stopWatch();
        });
    }
    const isToday = computed(() => selected.value === dayjs.unix(activityTimeStore.serverTime).format('YYYY-MM-DD'));

    return {
        totalKey: TOTAL,
        dates,
        reset,
        selected,
        selectTotal,
        selectDate,
        init,
        isToday,
        resetNull,
    };
});

export default useDateSelect;
