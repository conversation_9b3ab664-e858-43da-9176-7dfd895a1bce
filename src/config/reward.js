export const rewardList = [
    {
        resource_id: 'A1',
        name: '测试2',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A1.png',
    },
    {
        resource_id: 'A2',
        name: '环游领航员',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A2.png',
    },
    {
        resource_id: 'A3',
        name: '山河探游录当红赛区TOP1达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A3.png',
    },
    {
        resource_id: 'A4',
        name: '山河探游录当红赛区TOP2达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A4.png',
    },
    {
        resource_id: 'A5',
        name: '山河探游录当红赛区TOP3达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A5.png',
    },
    {
        resource_id: 'A6',
        name: '山河探游录当红赛区十强达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A6.png',
    },
    {
        resource_id: 'A7',
        name: '山河探游录新锐赛区TOP1达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A7.png',
    },
    {
        resource_id: 'A8',
        name: '山河探游录新锐赛区TOP2达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A8.png',
    },
    {
        resource_id: 'A9',
        name: '山河探游录新锐赛区TOP3达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A9.png',
    },
    {
        resource_id: 'A10',
        name: '山河探游录新锐赛区十强达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A10.png',
    },
    {
        resource_id: 'A11',
        name: '山河探游录虚拟偶像榜TOP1达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A11.png',
    },
    {
        resource_id: 'A12',
        name: '山河探游录虚拟偶像榜TOP2达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A12.png',
    },
    {
        resource_id: 'A13',
        name: '千灯月夜',
        type: 'fans_plate',
        mark: '粉丝团铭牌',
        unit: '天',
        special_type: '',
        image: 'A13.png',
    },
    {
        resource_id: 'A14',
        type: 'intimacy_multiple',
        mark: '亲密值加倍',
        unit: '天',
        special_type: '2倍',
        image: 'A14.png',
    },
    {
        resource_id: 'B1',
        type: 'other',
        mark: '公会积分',
        unit: '天',
        special_type: '',
        image: 'B1.png',
    },
];

export const rewardMap = {
    A1: {
        resource_id: 'A1',
        name: '测试2',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A1.png',
    },
    A2: {
        resource_id: 'A2',
        name: '环游领航员',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A2.png',
    },
    A3: {
        resource_id: 'A3',
        name: '山河探游录当红赛区TOP1达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A3.png',
    },
    A4: {
        resource_id: 'A4',
        name: '山河探游录当红赛区TOP2达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A4.png',
    },
    A5: {
        resource_id: 'A5',
        name: '山河探游录当红赛区TOP3达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A5.png',
    },
    A6: {
        resource_id: 'A6',
        name: '山河探游录当红赛区十强达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A6.png',
    },
    A7: {
        resource_id: 'A7',
        name: '山河探游录新锐赛区TOP1达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A7.png',
    },
    A8: {
        resource_id: 'A8',
        name: '山河探游录新锐赛区TOP2达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A8.png',
    },
    A9: {
        resource_id: 'A9',
        name: '山河探游录新锐赛区TOP3达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A9.png',
    },
    A10: {
        resource_id: 'A10',
        name: '山河探游录新锐赛区十强达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A10.png',
    },
    A11: {
        resource_id: 'A11',
        name: '山河探游录虚拟偶像榜TOP1达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A11.png',
    },
    A12: {
        resource_id: 'A12',
        name: '山河探游录虚拟偶像榜TOP2达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A12.png',
    },
    A13: {
        resource_id: 'A13',
        name: '千灯月夜',
        type: 'fans_plate',
        mark: '粉丝团铭牌',
        unit: '天',
        special_type: '',
        image: 'A13.png',
    },
    A14: {
        resource_id: 'A14',
        type: 'intimacy_multiple',
        mark: '亲密值加倍',
        unit: '天',
        special_type: '2倍',
        image: 'A14.png',
    },
    B1: {
        resource_id: 'B1',
        type: 'other',
        mark: '公会积分',
        unit: '天',
        special_type: '',
        image: 'B1.png',
    },
};
