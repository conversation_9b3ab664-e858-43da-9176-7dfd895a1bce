<template>
    <div
        v-if="!hiddenStatus && props.cid && statusClassName"
        class="room-status"
        :class="[statusClassName]"
        @click.stop="toRoom(props.cid)"
    >
    </div>
</template>

<script setup>
import { toRoom } from '@/utils/jsbridge';

const props = defineProps({
    // 房间状态
    status: {
        type: [Number, String],
        default: 0,
    },
    // 房间号
    cid: {
        type: [Number, String],
        default: 0,
    },
    // 是否为直播
    isAnchor: {
        type: Boolean,
        default: true,
    },
    // 是否隐藏
    hiddenStatus: {
        type: Boolean,
        default: false,
    },
});

const STATUS = {
    IN_ROOM: 1, // 房间中
    WATCHING: 2, // 看直播
    LIVING: 3, // 直播中
    PK: 4, // pk 中
};

const curText = computed(() => {
    const textMap = {
        [STATUS.IN_ROOM]: '房间中',
        [STATUS.WATCHING]: '看直播',
        [STATUS.LIVING]: '直播中',
        [STATUS.PK]: 'PK中',
    };
    return textMap[props.status] || '';
});

const statusClassName = computed(() => {
    const classMap = {
        [STATUS.IN_ROOM]: props.isAnchor ? '' : '',
        [STATUS.LIVING]: props.isAnchor ? 'living' : '',
        [STATUS.WATCHING]: props.isAnchor ? 'watching' : 'watching',
        [STATUS.PK]: props.isAnchor ? 'pk' : '',
    };
    return classMap[props.status] || '';
});
</script>

<style lang="less" scoped>
.room-status {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 10px;
    width: 48px;
    height: 16px;
    background-size: 100% 100%;
    font-weight: normal;
    text-align: center;
    line-height: 12.5px;
    font-weight: normal;
    text-align: center;
    color: #363636;
}

.living {
    background-image: url('@/assets/img/<EMAIL>');
}

.pk {
    background-image: url('@/assets/img/<EMAIL>');
}

.watching {
    background-image: url('@/assets/img/<EMAIL>');
}

.room {
    // background-image: url('@/assets/img/<EMAIL>');
}
</style>
