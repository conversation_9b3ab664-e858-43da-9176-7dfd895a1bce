// src/components/time-select/test-utils.js
// 时间选择组件的测试工具函数

import dayjs from '@/utils/dayjs';

/**
 * 模拟活动时间数据
 */
export function createMockActivityTime(options = {}) {
    const {
        startTime = dayjs('2025-09-09 18:00:00').unix(),
        endTime = dayjs('2025-09-21 23:59:59').unix(),
        serverTime = dayjs().unix(),
    } = options;

    return {
        startTime,
        endTime,
        serverTime,
        isInActivityDate: true,
        isEnd: false,
        isInStartDay: true,
        activityDates: generateActivityDates(startTime, endTime),
    };
}

/**
 * 生成活动日期数组
 */
function generateActivityDates(startTime, endTime) {
    const dates = [];
    let current = dayjs.unix(startTime).startOf('day');
    const end = dayjs.unix(endTime).startOf('day');

    while (current.isSameOrBefore(end)) {
        dates.push(current);
        current = current.add(1, 'day');
    }

    return dates;
}

/**
 * 测试时间段选择逻辑
 */
export function testTimeRangeSelection() {
    console.group('时间段选择测试');

    // 测试6个时间段的生成
    const hours = [18, 19, 20, 21, 22, 23];
    const timeRanges = hours.map((h) => {
        const start = dayjs().hour(h).minute(0).second(0).millisecond(0);
        const end = start.clone().add(1, 'hour');
        return {
            range: [start.format('HH'), end.format('HH')],
            value: start.format('HH:mm:ss'),
            rangeStr: `${start.format('HH')}-${end.format('HH')}点`,
            hour: h,
        };
    });

    console.log('生成的时间段:', timeRanges);

    // 测试当前时间段检测
    const currentHour = dayjs().hour();
    const currentRange = timeRanges.find(item => currentHour >= item.hour && currentHour < item.hour + 1);
    console.log('当前时间段:', currentRange);

    console.groupEnd();
    return timeRanges;
}

/**
 * 测试日期选择逻辑
 */
export function testDateSelection() {
    console.group('日期选择测试');

    const mockActivity = createMockActivityTime();
    const dates = mockActivity.activityDates.map((date, index) => ({
        date,
        value: date.format('YYYY-MM-DD'),
        dateStr: date.format('YYYY-MM-DD'),
    }));

    console.log('可选日期:', dates);

    // 测试默认日期选择逻辑
    const serverTime = mockActivity.serverTime;
    const currentHour = dayjs.unix(serverTime).hour();

    let defaultDate;
    if (currentHour < 18) {
        // 当前未到18点，使用前一天的时间
        defaultDate = dayjs.unix(serverTime).subtract(1, 'day').format('YYYY-MM-DD');
    }
    else {
        defaultDate = dayjs.unix(serverTime).format('YYYY-MM-DD');
    }

    console.log('默认选择日期:', defaultDate);
    console.log('当前小时:', currentHour);

    console.groupEnd();
    return { dates, defaultDate };
}

/**
 * 测试边界情况
 */
export function testEdgeCases() {
    console.group('边界情况测试');

    // 测试活动开始前
    const beforeActivity = createMockActivityTime({
        serverTime: dayjs('2025-09-09 17:00:00').unix(),
    });
    console.log('活动开始前:', beforeActivity);

    // 测试活动结束后
    const afterActivity = createMockActivityTime({
        serverTime: dayjs('2025-09-22 10:00:00').unix(),
        isEnd: true,
        isInActivityDate: false,
    });
    console.log('活动结束后:', afterActivity);

    // 测试第一个时间段
    const firstTimeRange = dayjs('2025-09-15 18:30:00');
    console.log('第一个时间段测试:', {
        time: firstTimeRange.format('YYYY-MM-DD HH:mm:ss'),
        hour: firstTimeRange.hour(),
        shouldSelectFirst: firstTimeRange.hour() >= 18 && firstTimeRange.hour() < 19,
    });

    // 测试最后一个时间段
    const lastTimeRange = dayjs('2025-09-15 23:30:00');
    console.log('最后一个时间段测试:', {
        time: lastTimeRange.format('YYYY-MM-DD HH:mm:ss'),
        hour: lastTimeRange.hour(),
        shouldSelectLast: lastTimeRange.hour() >= 23,
    });

    console.groupEnd();
}

/**
 * 验证时间段切换逻辑
 */
export function testTimeRangeSwitching() {
    console.group('时间段切换测试');

    const timeRanges = testTimeRangeSelection();
    let currentIndex = 2; // 假设当前在第3个时间段 (20-21点)

    console.log('当前时间段索引:', currentIndex);
    console.log('当前时间段:', timeRanges[currentIndex]);

    // 测试上一时间段
    const prevIndex = currentIndex - 1;
    if (prevIndex >= 0) {
        console.log('上一时间段:', timeRanges[prevIndex]);
    }
    else {
        console.log('已经是第一个时间段，无法切换到上一个');
    }

    // 测试下一时间段
    const nextIndex = currentIndex + 1;
    if (nextIndex < timeRanges.length) {
        console.log('下一时间段:', timeRanges[nextIndex]);
    }
    else {
        console.log('已经是最后一个时间段，无法切换到下一个');
    }

    console.groupEnd();
}

/**
 * 运行所有测试
 */
export function runAllTests() {
    console.log('=== 时间选择组件测试开始 ===');

    testTimeRangeSelection();
    testDateSelection();
    testEdgeCases();
    testTimeRangeSwitching();

    console.log('=== 时间选择组件测试结束 ===');
}

/**
 * 格式化选择结果
 */
export function formatSelection(selection) {
    if (!selection.date || !selection.time) {
        return '未完整选择';
    }

    const dateText = dayjs(selection.date).format('MM月DD日');
    const timeText = selection.timeText || selection.time;
    const statusText = selection.isHistory ? ' (历史数据)' : '';

    return `${dateText} ${timeText}${statusText}`;
}

/**
 * 验证选择是否有效
 */
export function validateSelection(selection, activityTime) {
    const errors = [];

    if (!selection.date) {
        errors.push('未选择日期');
    }

    if (!selection.time) {
        errors.push('未选择时间段');
    }

    if (selection.date && selection.time) {
        const selectedDateTime = dayjs(`${selection.date} ${selection.time}`);
        const serverTime = dayjs.unix(activityTime.serverTime);

        // 检查是否选择了未来时间
        if (selectedDateTime.isAfter(serverTime)) {
            errors.push('不能选择未来时间');
        }

        // 检查是否在活动时间范围内
        const startTime = dayjs.unix(activityTime.startTime);
        const endTime = dayjs.unix(activityTime.endTime);

        if (selectedDateTime.isBefore(startTime)) {
            errors.push('选择时间早于活动开始时间');
        }

        if (selectedDateTime.isAfter(endTime)) {
            errors.push('选择时间晚于活动结束时间');
        }
    }

    return {
        isValid: errors.length === 0,
        errors,
    };
}

// 导出默认测试函数
export default runAllTests;
