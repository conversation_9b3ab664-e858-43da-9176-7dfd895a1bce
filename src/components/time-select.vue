<!-- src/components/time-select.vue -->
<template>
    <div class="time-select">
        <!-- 日期选择区域 -->
        <div class="date-section">
            <div class="date-picker">
                <select 
                    v-model="timeStore.selectedDate" 
                    class="date-select"
                    @change="handleDateChange">
                    <option 
                        v-for="date in timeStore.dates" 
                        :key="date.value" 
                        :value="date.value">
                        {{ date.dateStr }}
                    </option>
                </select>
            </div>
        </div>

        <!-- 时间段选择区域 -->
        <div class="time-section">
            <!-- 箭头切换 -->
            <div class="time-controls">
                <button 
                    class="time-btn prev-btn" 
                    @click="handlePrevTime">
                    <span class="arrow-left">‹</span>
                    上一时段
                </button>
                
                <!-- 当前时间段显示和下拉选择 -->
                <div class="time-display-wrapper">
                    <div 
                        class="time-display" 
                        @click="toggleTimeDropdown">
                        {{ currentTimeRangeText }}
                        <span class="dropdown-arrow" :class="{ 'open': showTimeDropdown }">▼</span>
                    </div>
                    
                    <!-- 时间段下拉框 -->
                    <div v-show="showTimeDropdown" class="time-dropdown">
                        <div class="dropdown-header">
                            <span class="dropdown-title">选择时段</span>
                            <button class="close-btn" @click="closeDropdown">×</button>
                        </div>
                        <div
                            v-for="timeRange in timeStore.timeRanges"
                            :key="timeRange.value"
                            class="time-option"
                            :class="{ 'active': timeRange.value === timeStore.selectedTimeRange }"
                            @click="handleTimeRangeSelect(timeRange.value)">
                            {{ timeRange.rangeStr }}
                        </div>
                    </div>
                </div>
                
                <button 
                    class="time-btn next-btn" 
                    @click="handleNextTime">
                    下一时段
                    <span class="arrow-right">›</span>
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import useTimeSelect from '@/stores/use-time-select-store';

// Props
const props = defineProps({
    // 是否自动初始化
    autoInit: {
        type: Boolean,
        default: true,
    },
});

// Emits
const emit = defineEmits(['dateChange', 'timeChange']);

// Store
const timeStore = useTimeSelect();

// 响应式数据
const showTimeDropdown = ref(false);

// 计算属性
const currentTimeRangeText = computed(() => {
    if (!timeStore.selectedTimeRange) {
        return '请选择时段';
    }
    const currentRange = timeStore.timeRanges.find(
        item => item.value === timeStore.selectedTimeRange
    );
    return currentRange?.rangeStr || '请选择时段';
});

// 方法
function handleDateChange() {
    emit('dateChange', timeStore.selectedDate);
}

function handlePrevTime() {
    timeStore.prevTimeRange();
    emit('timeChange', timeStore.selectedTimeRange);
}

function handleNextTime() {
    timeStore.nextTimeRange();
    emit('timeChange', timeStore.selectedTimeRange);
}

function toggleTimeDropdown() {
    showTimeDropdown.value = !showTimeDropdown.value;
}

function handleTimeRangeSelect(value) {
    timeStore.changeTimeRange(value);
    // 注意：不自动关闭下拉框，需要手动点击才能收起
    emit('timeChange', timeStore.selectedTimeRange);
}

// 点击外部关闭下拉框
function handleClickOutside(event) {
    const dropdown = event.target.closest('.time-display-wrapper');
    if (!dropdown) {
        showTimeDropdown.value = false;
    }
}

// 手动关闭下拉框
function closeDropdown() {
    showTimeDropdown.value = false;
}

// 生命周期
onMounted(() => {
    if (props.autoInit) {
        timeStore.init();
    }
    // 添加全局点击事件监听
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    // 移除全局点击事件监听
    document.removeEventListener('click', handleClickOutside);
});

// 监听选中值变化
watch(() => timeStore.selectedDate, (newDate) => {
    emit('dateChange', newDate);
});

watch(() => timeStore.selectedTimeRange, (newTime) => {
    emit('timeChange', newTime);
});
</script>

<style lang="less" scoped>
.time-select {
    width: 100%;
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.date-section {
    margin-bottom: 16px;
    
    .date-picker {
        .date-select {
            width: 100%;
            height: 40px;
            padding: 0 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background-color: #fff;
            cursor: pointer;
            
            &:focus {
                outline: none;
                border-color: #007aff;
            }
        }
    }
}

.time-section {
    .time-controls {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
        
        .time-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #fff;
            font-size: 12px;
            color: #666;
            cursor: pointer;
            transition: all 0.2s;
            
            &:hover {
                background-color: #f5f5f5;
                border-color: #007aff;
            }
            
            &:active {
                background-color: #e5e5e5;
            }
            
            .arrow-left,
            .arrow-right {
                font-size: 16px;
                font-weight: bold;
            }
        }
        
        .time-display-wrapper {
            position: relative;
            flex: 1;
            
            .time-display {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                height: 40px;
                padding: 0 16px;
                border: 1px solid #ddd;
                border-radius: 6px;
                background-color: #fff;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;
                
                &:hover {
                    border-color: #007aff;
                }
                
                .dropdown-arrow {
                    font-size: 12px;
                    transition: transform 0.2s;
                    
                    &.open {
                        transform: rotate(180deg);
                    }
                }
            }
            
            .time-dropdown {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                z-index: 1000;
                margin-top: 4px;
                border: 1px solid #ddd;
                border-radius: 6px;
                background-color: #fff;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                max-height: 240px;
                overflow: hidden;

                .dropdown-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 12px 16px;
                    background-color: #f8f9fa;
                    border-bottom: 1px solid #e9ecef;

                    .dropdown-title {
                        font-size: 14px;
                        font-weight: 500;
                        color: #333;
                    }

                    .close-btn {
                        width: 24px;
                        height: 24px;
                        border: none;
                        border-radius: 50%;
                        background-color: #e9ecef;
                        color: #666;
                        font-size: 16px;
                        font-weight: bold;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.2s;

                        &:hover {
                            background-color: #dee2e6;
                            color: #333;
                        }
                    }
                }

                .time-option {
                    padding: 12px 16px;
                    font-size: 14px;
                    cursor: pointer;
                    transition: background-color 0.2s;

                    &:hover {
                        background-color: #f5f5f5;
                    }

                    &.active {
                        background-color: #007aff;
                        color: #fff;

                        &:hover {
                            background-color: #0056cc;
                        }
                    }

                    &:not(:last-child) {
                        border-bottom: 1px solid #f0f0f0;
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 375px) {
    .time-select {
        padding: 12px;
    }
    
    .time-controls {
        .time-btn {
            padding: 6px 8px;
            font-size: 11px;
        }
        
        .time-display {
            font-size: 13px;
        }
    }
}
</style>
