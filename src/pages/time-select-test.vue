<!-- src/pages/time-select-test.vue -->
<!-- 时间选择组件集成测试页面 -->
<template>
    <page-container>
        <div class="test-page">
            <h1 class="test-title">Time Select 组件测试</h1>

            <!-- 测试控制面板 -->
            <div class="test-controls">
                <h2>测试控制</h2>
                <div class="control-buttons">
                    <button
                        class="test-btn"
                        @click="runBasicTests">
                        运行基础测试
                    </button>
                    <button
                        class="test-btn"
                        @click="runEdgeTests">
                        运行边界测试
                    </button>
                    <button
                        class="test-btn secondary"
                        @click="clearLogs">
                        清空日志
                    </button>
                </div>
            </div>

            <!-- 基础组件测试 -->
            <div class="test-section">
                <h2>基础组件测试</h2>
                <time-select
                    ref="timeSelectRef"
                    :auto-init="false"
                    @date-change="onDateChange"
                    @time-change="onTimeChange" />

                <div class="test-actions">
                    <button
                        class="action-btn"
                        @click="initTimeSelect">
                        初始化
                    </button>
                    <button
                        class="action-btn"
                        @click="resetTimeSelect">
                        重置
                    </button>
                    <button
                        class="action-btn"
                        @click="testPrevTime">
                        上一时段
                    </button>
                    <button
                        class="action-btn"
                        @click="testNextTime">
                        下一时段
                    </button>
                </div>
            </div>

            <!-- 业务组件测试 -->
            <div class="test-section">
                <h2>业务组件测试</h2>
                <rank-time-selector
                    ref="rankSelectorRef"
                    @confirm="onRankConfirm"
                    @change="onRankChange"
                    @reset="onRankReset" />
            </div>

            <!-- 当前状态显示 -->
            <div class="status-section">
                <h2>当前状态</h2>
                <div class="status-grid">
                    <div class="status-item">
                        <label>选中日期：</label>
                        <span>{{ timeStore.selectedDate || '未选择' }}</span>
                    </div>
                    <div class="status-item">
                        <label>选中时段：</label>
                        <span>{{ selectedTimeText || '未选择' }}</span>
                    </div>
                    <div class="status-item">
                        <label>是否今天：</label>
                        <span>{{ timeStore.isToday ? '是' : '否' }}</span>
                    </div>
                    <div class="status-item">
                        <label>时段索引：</label>
                        <span>{{ timeStore.getCurrentTimeRangeIndex() }}</span>
                    </div>
                    <div class="status-item">
                        <label>服务器时间：</label>
                        <span>{{ serverTimeText }}</span>
                    </div>
                    <div class="status-item">
                        <label>活动状态：</label>
                        <span>{{ activityStatus }}</span>
                    </div>
                </div>
            </div>

            <!-- 测试日志 -->
            <div class="log-section">
                <h2>测试日志</h2>
                <div
                    ref="logContainer"
                    class="log-container">
                    <div
                        v-for="(log, index) in testLogs"
                        :key="index"
                        class="log-item"
                        :class="log.type">
                        <span class="log-time">{{ log.time }}</span>
                        <span class="log-message">{{ log.message }}</span>
                    </div>
                </div>
            </div>
        </div>
    </page-container>
</template>

<script setup>
import TimeSelect from '@/components/tab1/time-select.vue';
import RankTimeSelector from '@/components/tab1/rank-time-selector.vue';
import useTimeSelect from '@/stores/use-time-select-store';
import useActivityTime from '@/stores/modules/use-activity-time';
import { formatSelection, runAllTests, testEdgeCases, testTimeRangeSelection, validateSelection } from '@/components/time-select/test-utils';
import dayjs from '@/utils/dayjs';

// Store
const timeStore = useTimeSelect();
const activityTimeStore = useActivityTime();

// Refs
const timeSelectRef = ref(null);
const rankSelectorRef = ref(null);
const logContainer = ref(null);

// 响应式数据
const testLogs = ref([]);

// 计算属性
const selectedTimeText = computed(() => {
    if (!timeStore.selectedTimeRange)
        return '';
    const timeRange = timeStore.timeRanges.find(item => item.value === timeStore.selectedTimeRange);
    return timeRange?.rangeStr || '';
});

const serverTimeText = computed(() => {
    if (!activityTimeStore.serverTime)
        return '暂无';
    return dayjs.unix(activityTimeStore.serverTime).format('YYYY-MM-DD HH:mm:ss');
});

const activityStatus = computed(() => {
    if (activityTimeStore.notStarted)
        return '未开始';
    if (activityTimeStore.isEnd)
        return '已结束';
    if (activityTimeStore.isInActivityDate)
        return '进行中';
    return '未知';
});

// 方法
function addLog(message, type = 'info') {
    const log = {
        time: dayjs().format('HH:mm:ss'),
        message,
        type,
    };
    testLogs.value.push(log);

    // 自动滚动到底部
    nextTick(() => {
        if (logContainer.value) {
            logContainer.value.scrollTop = logContainer.value.scrollHeight;
        }
    });
}

function clearLogs() {
    testLogs.value = [];
    addLog('日志已清空', 'info');
}

// 基础测试
function runBasicTests() {
    addLog('开始运行基础测试...', 'info');

    try {
        // 测试时间段生成
        const timeRanges = testTimeRangeSelection();
        addLog(`生成了 ${timeRanges.length} 个时间段`, 'success');

        // 测试当前时间段检测
        const currentHour = dayjs().hour();
        const currentRange = timeRanges.find(item => currentHour >= item.hour && currentHour < item.hour + 1);
        if (currentRange) {
            addLog(`当前时间段: ${currentRange.rangeStr}`, 'success');
        }
        else {
            addLog(`当前时间 ${currentHour}:xx 不在任何时间段内`, 'warning');
        }

        // 测试日期数组
        addLog(`可选日期数量: ${timeStore.dates.length}`, 'success');

        addLog('基础测试完成', 'success');
    }
    catch (error) {
        addLog(`基础测试失败: ${error.message}`, 'error');
    }
}

function runEdgeTests() {
    addLog('开始运行边界测试...', 'info');

    try {
        testEdgeCases();

        // 测试边界切换
        const currentIndex = timeStore.getCurrentTimeRangeIndex();
        addLog(`当前时段索引: ${currentIndex}`, 'info');

        if (currentIndex <= 0) {
            addLog('当前在第一个时段，测试上一时段边界', 'warning');
        }

        if (currentIndex >= timeStore.timeRanges.length - 1) {
            addLog('当前在最后一个时段，测试下一时段边界', 'warning');
        }

        addLog('边界测试完成', 'success');
    }
    catch (error) {
        addLog(`边界测试失败: ${error.message}`, 'error');
    }
}

// 组件操作
function initTimeSelect() {
    timeStore.init();
    addLog('时间选择组件已初始化', 'info');
}

function resetTimeSelect() {
    timeStore.resetNull();
    addLog('时间选择组件已重置', 'info');
}

function testPrevTime() {
    const beforeIndex = timeStore.getCurrentTimeRangeIndex();
    timeStore.prevTimeRange();
    const afterIndex = timeStore.getCurrentTimeRangeIndex();

    if (beforeIndex === afterIndex && beforeIndex <= 0) {
        addLog('已在第一个时段，无法切换到上一时段', 'warning');
    }
    else {
        addLog(`从时段 ${beforeIndex} 切换到 ${afterIndex}`, 'success');
    }
}

function testNextTime() {
    const beforeIndex = timeStore.getCurrentTimeRangeIndex();
    timeStore.nextTimeRange();
    const afterIndex = timeStore.getCurrentTimeRangeIndex();

    if (beforeIndex === afterIndex && beforeIndex >= timeStore.timeRanges.length - 1) {
        addLog('已在最后一个时段，无法切换到下一时段', 'warning');
    }
    else {
        addLog(`从时段 ${beforeIndex} 切换到 ${afterIndex}`, 'success');
    }
}

// 事件处理
function onDateChange(date) {
    addLog(`日期变更: ${date}`, 'info');
}

function onTimeChange(time) {
    addLog(`时间段变更: ${time}`, 'info');
}

function onRankConfirm(selection) {
    const formatted = formatSelection(selection);
    addLog(`榜单时间确认: ${formatted}`, 'success');

    // 验证选择
    const validation = validateSelection(selection, activityTimeStore);
    if (validation.isValid) {
        addLog('选择验证通过', 'success');
    }
    else {
        addLog(`选择验证失败: ${validation.errors.join(', ')}`, 'error');
    }
}

function onRankChange(selection) {
    const formatted = formatSelection(selection);
    addLog(`榜单时间变更: ${formatted}`, 'info');
}

function onRankReset() {
    addLog('榜单时间选择已重置', 'info');
}

// 生命周期
onMounted(() => {
    addLog('测试页面已加载', 'info');

    // 运行控制台测试
    runAllTests();
});
</script>

<style lang="less" scoped>
.test-page {
    padding: 16px;
    max-width: 375px;
    margin: 0 auto;
    background-color: #f5f6fa;
    min-height: 100vh;
}

.test-title {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
}

.test-section,
.status-section,
.log-section,
.test-controls {
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;

    h2 {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 12px 0;
        color: #333;
    }
}

.control-buttons,
.test-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 12px;
}

.test-btn,
.action-btn {
    padding: 8px 12px;
    border: 1px solid #007aff;
    border-radius: 6px;
    background-color: #007aff;
    color: #fff;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
        background-color: #0056cc;
    }

    &.secondary {
        background-color: #f8f9fa;
        color: #666;
        border-color: #dee2e6;

        &:hover {
            background-color: #e9ecef;
        }
    }
}

.status-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;

    .status-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
            border-bottom: none;
        }

        label {
            font-weight: 500;
            color: #666;
        }

        span {
            color: #333;
        }
    }
}

.log-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    background-color: #f8f9fa;

    .log-item {
        display: flex;
        gap: 8px;
        padding: 4px 0;
        font-size: 12px;
        font-family: monospace;

        .log-time {
            color: #666;
            min-width: 60px;
        }

        .log-message {
            flex: 1;
        }

        &.info .log-message {
            color: #333;
        }

        &.success .log-message {
            color: #52c41a;
        }

        &.warning .log-message {
            color: #faad14;
        }

        &.error .log-message {
            color: #ff4d4f;
        }
    }
}
</style>
