<!-- src/pages/rank-page-example.vue -->
<!-- 榜单页面示例 - 展示如何使用时间选择组件 -->
<template>
    <page-container>
        <div class="rank-page">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">公会排行榜</h1>
                <div class="page-subtitle">实时更新，每小时统计一次</div>
            </div>

            <!-- 时间选择器 -->
            <rank-time-selector
                :show-actions="true"
                confirm-text="查看榜单"
                @confirm="handleTimeConfirm"
                @change="handleTimeChange"
                @reset="handleTimeReset" />

            <!-- 榜单内容区域 -->
            <div class="rank-content">
                <div
                    v-if="loading"
                    class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                </div>

                <div
                    v-else-if="!currentSelection.date"
                    class="empty-state">
                    <p>请选择要查看的日期和时间段</p>
                </div>

                <div
                    v-else
                    class="rank-list">
                    <!-- 当前查看信息 -->
                    <div class="current-viewing">
                        <div class="viewing-info">
                            <span class="viewing-text">正在查看：</span>
                            <span class="viewing-time">{{ currentSelection.dateText }} {{ currentSelection.timeText }}</span>
                            <span
                                v-if="currentSelection.isHistory"
                                class="history-tag">历史</span>
                        </div>
                        <div class="update-time">
                            <span>更新时间：{{ lastUpdateTime }}</span>
                        </div>
                    </div>

                    <!-- 模拟榜单数据 -->
                    <div class="rank-items">
                        <div
                            v-for="(item, index) in mockRankData"
                            :key="item.id"
                            class="rank-item"
                            :class="{ 'top-three': index < 3 }">
                            <div class="rank-number">{{ index + 1 }}</div>
                            <div class="guild-info">
                                <div class="guild-name">{{ item.name }}</div>
                                <div class="guild-score">{{ item.score }}分</div>
                            </div>
                            <div
                                class="rank-change"
                                :class="item.change > 0 ? 'up' : item.change < 0 ? 'down' : 'same'">
                                <span v-if="item.change > 0">↑{{ item.change }}</span>
                                <span v-else-if="item.change < 0">↓{{ Math.abs(item.change) }}</span>
                                <span v-else>-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="page-actions">
                <button
                    class="refresh-btn"
                    :disabled="loading"
                    @click="handleRefresh">
                    {{ loading ? '刷新中...' : '刷新数据' }}
                </button>
            </div>
        </div>
    </page-container>
</template>

<script setup>
import RankTimeSelector from '@/components/tab1/rank-time-selector.vue';
import dayjs from '@/utils/dayjs';

// 响应式数据
const loading = ref(false);
const currentSelection = ref({
    date: '',
    time: '',
    dateText: '',
    timeText: '',
    isHistory: false,
});
const lastUpdateTime = ref('');

// 模拟榜单数据
const mockRankData = ref([
    { id: 1, name: '龙腾四海', score: 999999, change: 2 },
    { id: 2, name: '凤舞九天', score: 888888, change: -1 },
    { id: 3, name: '霸气侧漏', score: 777777, change: 1 },
    { id: 4, name: '无敌战神', score: 666666, change: 0 },
    { id: 5, name: '天下无双', score: 555555, change: -2 },
    { id: 6, name: '王者归来', score: 444444, change: 3 },
    { id: 7, name: '至尊宝', score: 333333, change: 1 },
    { id: 8, name: '神话传说', score: 222222, change: -1 },
    { id: 9, name: '梦幻西游', score: 111111, change: 0 },
    { id: 10, name: '热血江湖', score: 99999, change: 2 },
]);

// 方法
function handleTimeConfirm(selection) {
    console.log('确认查看时间:', selection);
    currentSelection.value = selection;
    loadRankData();
}

function handleTimeChange(selection) {
    console.log('时间选择变化:', selection);
    // 可以在这里做一些实时预览或验证
}

function handleTimeReset() {
    console.log('重置时间选择');
    currentSelection.value = {
        date: '',
        time: '',
        dateText: '',
        timeText: '',
        isHistory: false,
    };
}

async function loadRankData() {
    if (!currentSelection.value.date || !currentSelection.value.time) {
        return;
    }

    loading.value = true;

    try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 模拟根据时间生成不同的数据
        const timeHash = currentSelection.value.date + currentSelection.value.time;
        const seed = timeHash.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);

        // 基于种子随机化数据
        mockRankData.value = mockRankData.value.map(item => ({
            ...item,
            score: item.score + (seed % 10000) - 5000,
            change: (seed % 7) - 3,
        })).sort((a, b) => b.score - a.score);

        lastUpdateTime.value = dayjs().format('HH:mm:ss');

        showToast('榜单数据加载完成');
    }
    catch (error) {
        console.error('加载榜单数据失败:', error);
        showToast('加载失败，请重试');
    }
    finally {
        loading.value = false;
    }
}

function handleRefresh() {
    if (currentSelection.value.date && currentSelection.value.time) {
        loadRankData();
    }
    else {
        showToast('请先选择查看时间');
    }
}

// 生命周期
onMounted(() => {
    console.log('榜单页面已加载');
});
</script>

<style lang="less" scoped>
.rank-page {
    padding: 16px;
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background-color: #f5f6fa;
}

.page-header {
    text-align: center;
    margin-bottom: 20px;

    .page-title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        margin: 0 0 8px 0;
    }

    .page-subtitle {
        font-size: 12px;
        color: #666;
    }
}

.rank-content {
    margin: 20px 0;

    .loading-state,
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        background-color: #fff;
        border-radius: 12px;

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007aff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        p {
            color: #666;
            font-size: 14px;
        }
    }

    .rank-list {
        background-color: #fff;
        border-radius: 12px;
        overflow: hidden;

        .current-viewing {
            padding: 16px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;

            .viewing-info {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 4px;

                .viewing-text {
                    font-size: 12px;
                    color: #666;
                }

                .viewing-time {
                    font-size: 14px;
                    font-weight: 500;
                    color: #333;
                }

                .history-tag {
                    padding: 2px 6px;
                    background-color: #ff6b6b;
                    color: #fff;
                    font-size: 10px;
                    border-radius: 4px;
                }
            }

            .update-time {
                font-size: 11px;
                color: #999;
            }
        }

        .rank-items {
            .rank-item {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                border-bottom: 1px solid #f0f0f0;

                &:last-child {
                    border-bottom: none;
                }

                &.top-three {
                    background-color: #fff9e6;
                }

                .rank-number {
                    width: 32px;
                    height: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                    font-size: 14px;
                    color: #666;
                    margin-right: 12px;
                }

                .guild-info {
                    flex: 1;

                    .guild-name {
                        font-size: 14px;
                        font-weight: 500;
                        color: #333;
                        margin-bottom: 2px;
                    }

                    .guild-score {
                        font-size: 12px;
                        color: #666;
                    }
                }

                .rank-change {
                    font-size: 12px;
                    font-weight: 500;

                    &.up {
                        color: #52c41a;
                    }

                    &.down {
                        color: #ff4d4f;
                    }

                    &.same {
                        color: #999;
                    }
                }
            }
        }
    }
}

.page-actions {
    margin-top: 20px;

    .refresh-btn {
        width: 100%;
        height: 44px;
        border: none;
        border-radius: 8px;
        background-color: #007aff;
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;

        &:hover:not(:disabled) {
            background-color: #0056cc;
        }

        &:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
</style>
