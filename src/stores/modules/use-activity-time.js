import { defineStore } from 'pinia';
import { computed, watch } from 'vue';
import isSameOrAfter from 'dayjs/esm/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/esm/plugin/isSameOrBefore';
import useInitStore from './use-init-store';
import dayjs from '@/utils/dayjs';
import { dayjsToDayBegin, dayjsToDayEnd, getIncludesDays } from '@/utils/index';
import resolveUnref from '@/use/resolve-unref';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

const useActivityTime = defineStore('activityTime', () => {
    const initStore = useInitStore();

    const startTime = computed(() => initStore.initData?.startTime); // 活动开始时间
    const endTime = computed(() => initStore.initData?.endTime); // 活动结束时间
    const serverTime = computed(() => initStore.serverTime || 0);

    const isEnd = computed(() => {
        const _serverTime = serverTime.value;

        const _endTime = endTime.value;

        if (_serverTime) {
            return _serverTime > _endTime;
        }
        return false;
    });

    const getDates = () => {
        let begin = dayjsToDayBegin(dayjs.unix(startTime.value).tz());
        const includesDays = getIncludesDays(startTime.value, endTime.value);
        const arr = [];
        for (let i = 0; i < includesDays; i++) {
            const cur = begin.clone();
            arr.push(cur);
            begin = begin.add(1, 'day');
        }
        return arr;
    };

    const checkIsNotStarted = (time) => {
        const target = dayjs(time).unix();
        return target < startTime.value;
    };

    const notStarted = computed(() => {
        if (!serverTime.value)
            return true;
        return serverTime.value < startTime.value;
    });

    const checkIsBeginDay = (timeIns) => {
        if (!startTime.value)
            return false;
        return timeIns.format('YYYY-MM-DD') === dayjs.unix(startTime.value).format('YYYY-MM-DD');
    };

    const isBeginDay = computed(() => {
        if (!serverTime.value)
            return false;
        return checkIsBeginDay(dayjs.unix(serverTime.value));
    });

    const activityDates = computed(() => {
        if (!startTime.value || !endTime.value)
            return [];
        const allDates = getDates();
        return allDates;
    });

    const isInActivityDate = computed(() => {
        if (!serverTime.value)
            return false;
        const beginT = dayjsToDayBegin(dayjs.unix(startTime.value));
        const endT = dayjsToDayEnd(dayjs.unix(endTime.value));
        const curT = dayjs.unix(serverTime.value);
        return curT.isSameOrAfter(beginT) && curT.isSameOrBefore(endT);
    });
    /**
     * 在服务器时间就绪情况下执行目标方法
     * @param {*} fn 目标函数
     * @returns
     */
    const serverTimeReady = (fn) => {
        const rawServerTime = resolveUnref(serverTime);
        if (rawServerTime) {
            fn?.();
            return;
        }
        const stopWatch = watch(
            () => resolveUnref(serverTime),
            () => {
                try {
                    fn?.();
                }
                catch (error) {
                    console.log(error);
                }
                finally {
                    stopWatch();
                }
            },
        );
    };

    const isInStartDay = computed(() => {
        if (!serverTime.value)
            return false;
        const t = dayjs.unix(serverTime.value);
        // 日期范围：含起
        const inDateRange = t.isSameOrAfter(dayjs('2025-09-09 19:00:00', 'Asia/Shanghai'));

        if (!inDateRange)
            return false;
        return true;
    });

    return {
        isBeginDay,
        activityDates,
        isInActivityDate,
        serverTime,
        startTime,
        notStarted,
        isEnd,
        checkIsNotStarted,
        checkIsBeginDay,
        serverTimeReady,
        isInStartDay,
    };
});

export default useActivityTime;
