import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import isSameOrAfter from 'dayjs/esm/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/esm/plugin/isSameOrBefore';
import useActivityTime from './use-activity-time';
import dayjs from '@/utils/dayjs';
import { checkInTargetDateTimeRange } from '@/utils/day-utils';
import { showToast } from '@/utils/utils';
import resolveUnref from '@/use/resolve-unref';
// import useDateSelectStore from './use-pk-date-select-store';
// import useInitStore from './use-init-store';
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

const genTimeRange = (start, end) => [start.format('HH'), end.format('HH')];

/**
 * 小时榜 时间选择：限制为每天 19-20 点、22-23 点
 */
const usePkTimeSelect = defineStore('pkTimeSelect', () => {
    const activityTimeStore = useActivityTime();
    const useDateSelectStore = require('./use-pk-date-select-store').default;
    const dateSelectStore = useDateSelectStore();

    const selected = ref();

    // 仅两个时间段：19-20、22-23
    const timeRanges = computed(() => {
        const hours = [19, 22];
        return hours.map((h) => {
            const start = dayjs.hour(h).minute(0).second(0).millisecond(0);
            const end = start.clone().add(1, 'hour');
            const range = genTimeRange(start, end);
            const rangeStr = `${range[0]}-${range[1]}点`;
            return {
                range,
                value: start.format('HH:mm:ss'),
                rangeStr,
            };
        });
    });

    /**
     * 当前时间所处的时间范围（基于服务器时间当天）
     */
    const currentTimeRange = computed(() => {
        const { serverTime } = activityTimeStore;
        if (!serverTime)
            return null;
        const rawTimeRanges = resolveUnref(timeRanges);
        const target = rawTimeRanges.find(item => checkInTargetDateTimeRange(dayjs.unix(serverTime), ...item.range));
        return target || null;
    });

    const changeTimeRange = (val) => {
        // 未开启时间处理：基于选中日期 + 选中小时
        const dateStr = dateSelectStore.selected;
        // 判断选中的是不是今天
        const isToday = dayjs.unix(activityTimeStore.serverTime).format('YYYY-MM-DD') === dateStr;
        if (!activityTimeStore.isInStartDay) {
            showToast('暂未开始');
            return;
        }
        if (
            isToday
            && dayjs
                .unix(activityTimeStore.serverTime)

                .isBefore(dayjs(`${dateStr} ${val}:00`))
        ) {
            showToast('暂未开始');
            return;
        }
        selected.value = val;
    };

    // 活动是否尚未到最早可选时段（以可选日期中的第一天 19:00 为准）
    const isBeforeHourRank = computed(() => {
        const { serverTime } = activityTimeStore;
        const allDates = dateSelectStore.dates;
        if (!serverTime || !allDates?.length)
            return true;
        const firstDate = allDates[0]?.value || dayjs.unix(serverTime).format('YYYY-MM-DD');
        return dayjs
            .unix(serverTime)

            .isBefore(dayjs(`${firstDate} 19:00:00`));
    });

    const initTimeRange = () => {
        const rawTimeRanges = resolveUnref(timeRanges);
        if (!rawTimeRanges?.length)
            return;

        if (dateSelectStore.isToday) {
            const now = dayjs.unix(activityTimeStore.serverTime);
            const h = now.hour();
            // 若 <19: 暂未开始，不选择
            if (h < 19)
                return;
            // 19、20、21 点：定位到 19 点
            if (h >= 19 && h < 22) {
                changeTimeRange(rawTimeRanges[0]?.value);
                return;
            }
            // ≥22 点：定位到 22 点
            changeTimeRange(rawTimeRanges[rawTimeRanges.length - 1]?.value);
            return;
        }
        // 非今日，且不在最早时间之前，默认 22 点
        if (!isBeforeHourRank.value) {
            const lastTimeRangeVal = rawTimeRanges[rawTimeRanges.length - 1]?.value;
            changeTimeRange(lastTimeRangeVal);
        }
    };

    const init = () => {
        initTimeRange();
    };

    return {
        selected,
        timeRanges,
        currentTimeRange,
        changeTimeRange,
        init,
        isBeforeHourRank,
    };
});

export default usePkTimeSelect;
